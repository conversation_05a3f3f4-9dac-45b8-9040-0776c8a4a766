import { Table, TableProps } from 'antd';
import { useState } from 'react';
import './table-control.less';

interface IBaseTableProps<RecordType> extends Omit<TableProps<RecordType>, 'dataSource' | 'loading'> {
  data: RecordType[];
  total: number;
  isLoading: boolean;
  defaultPageSize?: number;
  onPageChange?: (pageIndex: number, pageSize: number) => void;
}

const BaseTable = <RecordType extends object = any>(props: IBaseTableProps<RecordType>) => {
  const { data, total, isLoading, defaultPageSize = 10, onPageChange, pagination, ...restProps } = props;
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);

  const handlePageChange = (newPageIndex: number, size?: number) => {
    setPageIndex(newPageIndex);
    onPageChange?.(newPageIndex, size || pageSize);
  };

  const onShowSizeChange = (newPageIndex: number, size: number) => {
    setPageSize(size);
    setPageIndex(1); // Reset to first page when changing page size
    onPageChange?.(1, size);
  };

  // if (data.length === 0) {
  //   return <Empty description='Không có dữ liệu' />
  // }
  return (
    <div className="table-control">
      <Table<RecordType>
        {...restProps}
        scroll={{ x: 'max-content' }}
        dataSource={data}
        loading={isLoading}
        pagination={{
          current: pageIndex,
          pageSize,
          total,
          onChange: handlePageChange,
          showSizeChanger: true,
          onShowSizeChange,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          ...pagination,
        }}
      />
    </div>
  );
};

export default BaseTable;
