// Marketing-campaign components

import { DeleteOutlined } from '@ant-design/icons';
import { Col, Row } from 'antd';
import { useState } from 'react';

import { IEmployee, IEmployeeFilter } from '~/api/settting/employee/types';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import DetailButton from './components/detail-button';
import EditButton from './components/edit-button';
import FilterProduct from './components/filter-product';

export const EmployeeView = () => {
  const [filter, setFilter] = useState<IEmployeeFilter>({
    id: null,
    employeeCode: '',
    name: '',
    email: '',
    phone: '',
    department: '',
    position: '',
    dateOfBirth: '',
    gender: '',
    address: '',
    avatar: '',
    status: '',
    note: '',
    pageIndex: 0,
    pageSize: 10,
  });
  const handleFilter = (values: IEmployeeFilter) => {
    setFilter(values);
  };

  const handleReset = () => {
    setFilter({
      id: null,
      employeeCode: '',
      name: '',
      email: '',
      phone: '',
      department: '',
      position: '',
      dateOfBirth: '',
      gender: '',
      address: '',
      avatar: '',
      status: '',
      note: '',
      pageIndex: 0,
      pageSize: 10,
    });
  };
  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleDelete = async (item: IEmployee) => {
    try {
      // TODO: Implement delete functionality
      // toastService.success('Xóa thành công');
    } catch (error) {
      // toastService.handleError(error);
    }
  };
  const columns: any[] = [
    {
      title: 'Mã nhân viên',
      dataIndex: 'employeeCode',
      key: 'employeeCode',
      width: 150,
    },
    {
      title: 'Họ và tên',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 150,
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
    },
    {
      title: 'Phòng ban',
      dataIndex: 'department',
      key: 'department',
      width: 150,
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 150,
    },
    {
      title: 'Ngày sinh',
      dataIndex: 'dateOfBirth',
      key: 'dateOfBirth',
      width: 150,
    },
    {
      title: 'Giới tính',
      dataIndex: 'gender',
      key: 'gender',
      width: 150,
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 300,
    },
    {
      title: 'Ảnh đại diện',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 150,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 150,
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 150,
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 300,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type="primary"
            shape="circle"
            icon={<DeleteOutlined />}
            tooltip="Delete"
            onClick={() => handleDelete(record)}
          />
        </>
      ),
    },
  ];

  const [fakeData, setFakeData] = useState<IEmployee[]>([
    {
      id: '1',
      employeeCode: 'NV0001',
      name: 'Nguyễn Văn A',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=1',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '2',
      employeeCode: 'NV0002',
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=2',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '3',
      employeeCode: 'NV0003',
      name: 'Lê Văn C',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=3',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '4',
      employeeCode: 'NV0004',
      name: 'Phạm Thị D',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=4',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '5',
      employeeCode: 'NV0005',
      name: 'Hoàng Văn E',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=5',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '6',
      employeeCode: 'NV0006',
      name: 'Vũ Thị F',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=6',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '7',
      employeeCode: 'NV0007',
      name: 'Trịnh Văn G',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=7',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '8',
      employeeCode: 'NV0008',
      name: 'Lý Thị H',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=8',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '9',
      employeeCode: 'NV0009',
      name: 'Đỗ Văn I',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=9',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '10',
      employeeCode: 'NV0010',
      name: 'Ngô Thị J',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=10',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '11',
      employeeCode: 'NV0011',
      name: 'Võ Văn K',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=11',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '12',
      employeeCode: 'NV0012',
      name: 'Trần Văn L',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=12',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '13',
      employeeCode: 'NV0013',
      name: 'Lê Thị M',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=13',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '14',
      employeeCode: 'NV0014',
      name: 'Phạm Văn N',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=14',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '15',
      employeeCode: 'NV0015',
      name: 'Hoàng Thị O',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=15',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
    {
      id: '16',
      employeeCode: 'NV0016',
      name: 'Vũ Văn P',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=16',
      status: 'Hoạt động',
      note: 'Ghi chú',
    },
  ]);

  return (
    <BaseCard title="Thiết lập nhân viên">
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable columns={columns} data={fakeData} total={0} isLoading={false} onPageChange={handlePageChange} />
        </Col>
      </Row>
    </BaseCard>
  );
};
