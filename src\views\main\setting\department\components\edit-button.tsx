import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, Input, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';

import { useEffect } from 'react';
import { IDepartment } from '~/api/settting/department/types';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { useModal } from '~/hooks/use-modal';

const { Option } = Select;
const { TextArea } = Input;

interface EditButtonProps {
  data: IDepartment;
  onSuccess?: () => void;
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal();
  const [form] = useForm();
  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data,
      });
    }
  }, [open, data, form]);

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size="small">
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Mã phòng ban" name="id">
                <Input placeholder="Nhập mã phòng ban" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Tên phòng ban" name="name">
                <Input placeholder="Nhập tên phòng ban" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Mô tả" name="description">
                <Input placeholder="Nhập mô tả" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Trạng thái" name="status">
                <Select placeholder="Nhập trạng thái">
                  <Select.Option value="Hoạt động">Hoạt động</Select.Option>
                  <Select.Option value="Không hoạt động">Không hoạt động</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row
            style={{
              textAlign: 'center',
              borderTop: '1px solid #f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              padding: '16px 0',
            }}
          >
            <Button onClick={closeModal} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type="primary" icon={<SaveOutlined />}>
              Cập nhật
            </Button>
          </Row>
        </Form>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type="primary" tooltip="Chỉnh sửa" />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa phòng ban"
        description="Cập nhật thông tin phòng ban"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
