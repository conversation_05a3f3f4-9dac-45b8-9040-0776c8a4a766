import { DeleteOutlined, EditOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Divider, Form, Input, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';

import { ECustomer, NSCustomer } from '~/common/enums/customer.enum';
import { ModalContact } from './modal/modal-contact';

interface CreateCustomerModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export interface ContactInfo {
  id?: string;
  name: string;
  position?: NSCustomer.Position;
  isDecisionMaker?: boolean;
  email?: string;
  phone?: string;
  note?: string;
}

const CreateCustomerModal = ({ open, onClose, onSuccess }: CreateCustomerModalProps) => {
  const { t } = useTranslation();
  const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);
  const [form] = useForm();

  // Mock data cho UI
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [regions, setRegions] = useState([]);
  const [employees, setEmployees] = useState([
    { code: 'EMP001', name: 'Nguyễn Văn A', position: 'Nhân viên kinh doanh', email: '<EMAIL>' },
    { code: 'EMP002', name: 'Trần Thị B', position: 'Nhân viên kinh doanh', email: '<EMAIL>' },
    { code: 'EMP003', name: 'Lê Văn C', position: 'Trưởng phòng kinh doanh', email: '<EMAIL>' },
  ]);

  // State cho thông tin liên hệ
  const [contacts, setContacts] = useState<ContactInfo[]>([]);
  const [contactModalOpen, setContactModalOpen] = useState(false);
  const [editingContact, setEditingContact] = useState<ContactInfo | null>(null);
  const [contactForm] = useForm();

  const handleSave = async (values: any) => {
    if (!values) return;

    // Giả lập việc tạo khách hàng
    setIsCreatingCustomer(true);

    // Giả lập API call
    setTimeout(() => {
      setIsCreatingCustomer(false);
      onClose();
      form.resetFields();
      setContacts([]);
      if (onSuccess) onSuccess();
    }, 1000);
  };

  // Thêm mới contact
  const handleAddContact = () => {
    setEditingContact(null);
    contactForm.resetFields();
    setContactModalOpen(true);
  };

  // Sửa contact
  const handleEditContact = (contact: ContactInfo) => {
    setEditingContact(contact);
    contactForm.setFieldsValue(contact);
    setContactModalOpen(true);
  };

  // Xóa contact
  const handleDeleteContact = (contactId: string) => {
    setContacts(prev => prev.filter(c => c.id !== contactId));
  };

  // Lưu contact (thêm mới hoặc cập nhật)
  const handleSaveContact = (values: ContactInfo) => {
    if (editingContact) {
      // Cập nhật contact existing
      setContacts(prev => prev.map(c => (c.id === editingContact.id ? { ...values, id: editingContact.id } : c)));
    } else {
      // Thêm contact mới
      const newContact = {
        ...values,
        id: Date.now().toString(), // Simple ID generation
      };
      setContacts(prev => [...prev, newContact]);
    }
    setContactModalOpen(false);
    contactForm.resetFields();
  };

  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên người liên hệ',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 150,
      align: 'center',
    },
    {
      title: 'Người quyết định',
      dataIndex: 'isDecisionMaker',
      key: 'isDecisionMaker',
      width: 120,
      align: 'center',
      render: (value: boolean) => (value ? '✓' : ''),
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 150,
      align: 'center',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center',
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 150,
      align: 'center',
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (_, record: ContactInfo) => (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          <Button type="link" icon={<EditOutlined />} onClick={() => handleEditContact(record)} size="small" />
          <Button
            type="link"
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteContact(record.id!)}
            danger
            size="small"
          />
        </div>
      ),
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSave}>
      <Card title="Thông tin chung">
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              label="Tên khách hàng"
              name="name"
              rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng' }]}
            >
              <Input placeholder="Nhập Tên khách hàng" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Mã số thuế"
              name="taxNumber"
              rules={[{ required: true, message: 'Vui lòng nhập mã số thuế' }]}
            >
              <Input placeholder="Nhập Mã số thuế" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập số điện thoại',
                  pattern: /^[0-9]{10,11}$/,
                },
              ]}
            >
              <Input placeholder="Nhập Số điện thoại" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Email"
              name="email"
              rules={[
                {
                  required: true,
                  message: 'Vui lòng nhập email',
                  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                },
              ]}
            >
              <Input placeholder="Nhập Email" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Fax" name="fax">
              <Input placeholder="Nhập Fax" />
            </Form.Item>
          </Col>
          {/* <Col span={6}>
            <Form.Item
              label='Khu vực'
              name='region'
              rules={[{ required: true, message: 'Vui lòng chọn khu vực' }]}>
              <Select
                placeholder='-- Vui lòng chọn --'
                options={regions?.map((item) => ({
                  label: item.name,
                  value: item.code
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label='Tỉnh/Thành phố'
              name='provinceCode'
              rules={[{ required: true, message: 'Vui lòng chọn tỉnh/thành phố' }]}>
              <Select
                placeholder='-- Vui lòng chọn --'
                options={provinces?.map((item) => ({
                  label: item.name,
                  value: item.code.toString()
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label='Quận/Huyện'
              name='districtCode'
              rules={[{ required: true, message: 'Vui lòng chọn quận/huyện' }]}>
              <Select
                placeholder='-- Vui lòng chọn --'
                options={districts?.map((item) => ({
                  label: item.name,
                  value: item.code.toString()
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label='Phường/Xã'
              name='wardCode'
              rules={[{ required: true, message: 'Vui lòng chọn phường/xã' }]}>
              <Select
                placeholder='-- Vui lòng chọn --'
                options={wards?.map((item) => ({
                  label: item.name,
                  value: item.code.toString()
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label='Địa chỉ'
              name='address'
              rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}>
              <Input placeholder='Nhập Địa chỉ' />
            </Form.Item>
          </Col> */}
          <Col span={6}>
            <Form.Item label="Nguồn khách hàng" name="source">
              <Select
                placeholder="-- Vui lòng chọn --"
                options={Object.values(ECustomer.ECustomerSource).map((item: any) => ({
                  label: item.name,
                  value: item.value,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              label="Thị trường"
              name="market"
              rules={[{ required: true, message: 'Vui lòng chọn thị trường' }]}
            >
              <Select
                placeholder="-- Vui lòng chọn --"
                options={Object.values(ECustomer.ECustomerMarket).map((item: any) => ({
                  label: item.name,
                  value: item.value,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Nhân viên phụ trách"
              name="salesRep"
              rules={[{ required: true, message: 'Vui lòng chọn nhân viên phụ trách' }]}
            >
              <Select
                mode="multiple"
                placeholder="-- Vui lòng chọn --"
                options={employees?.map(item => ({
                  label: item.code + ' - ' + item.name + ' - ' + item.position + ' - ' + item.email,
                  value: item.code,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin liên hệ"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddContact}>
            Thêm mới
          </Button>
        }
      >
        <BaseTable columns={columns} data={contacts} total={contacts.length} isLoading={false} />
      </Card>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isCreatingCustomer}>
          Thêm mới
        </Button>
      </div>

      {/* Contact Modal */}
      <ModalContact
        editingContact={editingContact}
        contactModalOpen={contactModalOpen}
        setContactModalOpen={setContactModalOpen}
        contactForm={contactForm}
        handleSaveContact={handleSaveContact}
      />
    </Form>
  );

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title="Tạo khách hàng"
      description="Thêm khách hàng mới vào hệ thống"
      childrenBody={modalContent}
      width={1200} // Tăng width để hiển thị table tốt hơn
    />
  );
};

export default CreateCustomerModal;
