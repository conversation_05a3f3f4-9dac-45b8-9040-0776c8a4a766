import { CloseOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Form, Input, message, notification, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import useCustomer from '../../../Hooks/useCustomer';
import { ICustomer } from '~/api/customer/types';
import { NSCustomer } from '~/common/enums/customer.enum';
import helper from '~/common/helpers/helper';

type IProps = {
  onClose: () => void;
  data: ICustomer;
};

export const CustomerInfoTab = (props: IProps) => {
  const { onClose } = props;
  const [form] = useForm();
  const [isUpdatingCustomer, setIsUpdatingCustomer] = useState(false);

  const { updateCustomer } = useCustomer();
  const { data } = props;

  useEffect(() => {
    form.setFieldsValue(data);
  }, [data]);

  const handleSave = async (values: any) => {
    if (!values) return;
    // <PERSON><PERSON><PERSON> lập việc cập nhật khách hàng
    setIsUpdatingCustomer(true);

    const updateData = {
      ...data,
      ...values,
    };
    updateCustomer(updateData)
      .then(res => {
        if (res) {
          notification.success({
            message: 'Cập nhật thông tin khách hàng thành công',
            placement: 'top',
          });
          setIsUpdatingCustomer(false);
          onClose();
        }
      })
      .catch(() => {
        setIsUpdatingCustomer(false);
      });
  };

  return (
    <div>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        <Card title="Thông tin chung">
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                label="Tên khách hàng"
                name="name"
                rules={[{ required: true, message: 'Vui lòng nhập tên khách hàng' }]}
              >
                <Input placeholder="Nhập Tên khách hàng" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Mã số thuế"
                name="taxNumber"
                rules={[{ required: true, message: 'Vui lòng nhập mã số thuế' }]}
              >
                <Input placeholder="Nhập Mã số thuế" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Số điện thoại"
                name="phone"
                rules={[{ required: true, message: 'Vui lòng nhập số điện thoại', pattern: /^[0-9]*$/ }]}
              >
                <Input placeholder="Nhập Số điện thoại" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Email"
                name="email"
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập email',
                    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  },
                ]}
              >
                <Input placeholder="Nhập Email" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="Fax" name="fax">
                <Input placeholder="Nhập Fax" />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item label="Nguồn khách hàng" name="source">
                <Select placeholder="-- Vui lòng chọn --" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Thị trường"
                name="market"
                rules={[{ required: true, message: 'Vui lòng chọn thị trường' }]}
              >
                <Select placeholder="-- Vui lòng chọn --">
                  {helper.convertObjToArray(NSCustomer.MARKET).map(item => (
                    <Select.Option value={item.value}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Nhân viên phụ trách"
                name="salesRep"
                // rules={[{ required: true, message: 'Vui lòng chọn nhân viên phụ trách' }]}
              >
                <Select mode="multiple" placeholder="-- Vui lòng chọn --" />
              </Form.Item>
            </Col>
          </Row>
          <div
            style={{
              textAlign: 'center',
              marginTop: 24,
              paddingTop: 16,
            }}
          >
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isUpdatingCustomer}>
              Lưu thông tin chung
            </Button>
          </div>
        </Card>
      </Form>
      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button
          danger
          htmlType="button"
          icon={<CloseOutlined style={{ fontSize: 12 }} />}
          style={{ marginRight: 8 }}
          onClick={onClose}
        >
          Đóng
        </Button>
      </div>
    </div>
  );
};
