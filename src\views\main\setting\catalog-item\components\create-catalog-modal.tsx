import { SaveOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, Input, InputNumber, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseModal from '~/components/base-modal';

const { Option } = Select;
const { TextArea } = Input;

interface CreateItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// Dữ liệu giả cho danh sách sản phẩm
const mockCatalogs = [
  {
    id: '1',
    name: 'Sản phẩm A',
    unit: 'PIECE',
    unitPrice: 100000,
    description: 'Mô tả sản phẩm A',
  },
  {
    id: '2',
    name: 'Sản phẩm B',
    unit: 'BOX',
    unitPrice: 200000,
    description: '<PERSON><PERSON> tả sản phẩm B',
  },
];

const CreateCatalogModal = ({ open, onClose, onSuccess }: CreateItemModalProps) => {
  const [form] = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    form.resetFields();
  }, [open]);

  const handleCreate = async (values: any) => {
    if (!values) return;

    setIsSubmitting(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      // Thêm sản phẩm mới vào danh sách giả
      const newCatalog = {
        id: (mockCatalogs.length + 1).toString(),
        ...values,
      };

      mockCatalogs.push(newCatalog);
      console.log('Đã thêm sản phẩm mới:', newCatalog);
      console.log('Danh sách sản phẩm hiện tại:', mockCatalogs);

      setIsSubmitting(false);
      onClose();
      if (onSuccess) onSuccess();
    }, 500);
  };

  const modalContent = (
    <div>
      <Card style={{ marginBottom: '16px' }} size="small">
        <Form form={form} layout="vertical" onFinish={handleCreate}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Tên sản phẩm"
                name="name"
                rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
              >
                <Input placeholder="Nhập tên sản phẩm" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Đơn vị tính"
                name="unit"
                rules={[{ required: true, message: 'Vui lòng chọn đơn vị tính' }]}
              >
                <Select placeholder="Chọn đơn vị tính">
                  {Object.values(NSCatalog.EUnit).map(item => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Đơn giá"
                name="unitPrice"
                rules={[{ required: true, message: 'Vui lòng nhập đơn giá' }]}
              >
                <InputNumber
                  min={0}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  style={{ width: '100%' }}
                  placeholder="Nhập đơn giá"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Mô tả" name="description">
                <TextArea rows={4} placeholder="Nhập mô tả" />
              </Form.Item>
            </Col>
          </Row>

          <Row
            style={{
              textAlign: 'center',
              borderTop: '1px solid #f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              padding: '16px 0',
            }}
          >
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type="primary" icon={<SaveOutlined />} htmlType="submit" loading={isSubmitting}>
              Thêm mới
            </Button>
          </Row>
        </Form>
      </Card>
    </div>
  );

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title="Tạo sản phẩm mới"
      description="Thêm sản phẩm mới vào hệ thống"
      childrenBody={modalContent}
    />
  );
};

export default CreateCatalogModal;
