import { EyeOutlined } from '@ant-design/icons';
import { Card, Col, Row, Typography } from 'antd';
import { IDepartment } from '~/api/settting/department/types';
import { formatDateCustom } from '~/common/helpers/helper';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { useModal } from '~/hooks/use-modal';

const { Text } = Typography;

interface DetailButtonProps {
  data: IDepartment;
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal();

  const modalContent = (
    <div>
      {/* Product Overview Card */}
      <Card style={{ marginBottom: '16px' }} size="small">
        <Row gutter={16}>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mã phòng ban:</Text>
            <br />
            <Text>{data.id}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tên phòng ban:</Text>
            <br />
            <Text>{data.name}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mô tả:</Text>
            <br />
            <Text>{data.description}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Text>{data.status}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày tạo:</Text>
            <br />
            <Text>{formatDateCustom(data.createdAt, 'DD/MM/YYYY')}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày cập nhật:</Text>
            <br />
            <Text>{formatDateCustom(data.updatedAt, 'DD/MM/YYYY')}</Text>
          </Col>
        </Row>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type="primary" />
      <BaseModal
        open={open}
        title="Chi tiết nhân viên"
        description="Thông tin chi tiết nhân viên"
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  );
};

export default DetailButton;
