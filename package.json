{"name": "react-antd-admin", "type": "module", "version": "0.0.0", "private": true, "packageManager": "bun@1.2.12", "description": "React Hooks + TypeScript + Ant Design = react-antd-admin", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/condorheroblog/react-antd-admin/blob/main/README.md", "repository": {"type": "git", "url": "git+https://github.com/condorheroblog/react-antd-admin.git"}, "bugs": {"url": "https://github.com/condorheroblog/react-antd-admin/issues"}, "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "analyzer": "vite-bundle-visualizer --output analyzer/stats.html", "eslint-config-inspector": "eslint-config-inspector", "lint": "eslint .", "lint:fix": "node ./node_modules/.bin/eslint . --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "prepare": "simple-git-hooks", "npm-check": "taze major -lrw", "test": "vitest"}, "dependencies": {"@ant-design/icons": "6.0.0", "@ant-design/pro-components": "^2.8.10", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/react": "7.0.0-beta.0", "@tanstack/react-query": "^5.85.0", "@types/file-saver": "^2.0.7", "ahooks": "^3.9.0", "antd": "^5.27.0", "antd-img-crop": "^4.25.0", "axios": "^1.11.0", "dayjs": "^1.11.13", "echarts": "^6.0.0", "echarts-for-react": "^3.0.4", "file-saver": "^2.0.5", "fullcalendar": "7.0.0-beta.0", "html2pdf.js": "0.10.0", "i18next": "25.3.4", "keepalive-for-react": "^4.0.2", "moment": "^2.30.1", "motion": "^12.23.12", "nprogress": "^0.2.0", "pinyin-pro": "^3.27.0", "prettier": "^3.6.2", "react": "18", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "18", "react-error-boundary": "^6.0.0", "react-i18next": "^15.6.1", "react-jss": "^10.10.0", "react-quill": "^2.0.0", "react-router": "^7.8.0", "simplebar-react": "^3.3.2", "spin-delay": "^2.0.1", "tailwind-merge": "^2.6.0", "xlsx": "^0.18.5", "zustand": "^5.0.7"}, "devDependencies": {"@antfu/eslint-config": "^5.2.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint-react/eslint-plugin": "^1.36.1", "@eslint/config-inspector": "^1.2.0", "@faker-js/faker": "^9.9.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@tanstack/react-query-devtools": "^5.85.0", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.17.1", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.39.1", "@vitejs/plugin-react": "^5.0.0", "@vitejs/plugin-react-swc": "^4.0.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "code-inspector-plugin": "^1.1.1", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "happy-dom": "^18.0.1", "less": "^4.4.1", "lint-staged": "^16.1.5", "postcss": "^8.5.6", "simple-git-hooks": "^2.13.1", "tailwindcss": "^3.4.17", "taze": "^19.1.0", "typescript": "^5.9.2", "vite": "^7.1.2", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-checker": "^0.10.2", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.4"}, "simple-git-hooks": {"pre-commit": "bunx --bun lint-staged"}, "lint-staged": {"*": ["echo 'Skipping lint check temporarily'"]}}