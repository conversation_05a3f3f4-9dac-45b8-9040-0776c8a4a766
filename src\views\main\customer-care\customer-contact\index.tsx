// Marketing-campaign components

import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Col, Row } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';

import { IFilterCustomerContact } from '~/api/customer-care/complaint/types';
import { ICustomerContact } from '~/api/customer-care/customer-contact/types';
import BaseButton from '~/components/base-button';
import BaseCard from '~/components/base-card';
import BaseTable from '~/components/base-table';
import BaseView from '~/components/base-view';
import CreateCustomerSupportModal from './components/create-customer-support-modal';
import DetailButton from './components/detail-button';
import EditButton from './components/edit-button';
import FilterProduct from './components/filter-product';

export const CustomerContactView = () => {
  const [filter, setFilter] = useState<IFilterCustomerContact>({
    name: '',
    phone: '',
    email: '',
    pageIndex: 1,
    pageSize: 10,
  });
  const handleReset = () => {
    setFilter({
      name: '',
      phone: '',
      email: '',
      pageIndex: 1,
      pageSize: 10,
    });
  };
  const [visibleCreateModal, setVisibleCreateModal] = useState(false);

  const handleFilter = (values: IFilterCustomerContact) => {
    setFilter(values);
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  // fake data customer
  const [fakeData, setFakeData] = useState<ICustomerContact[]>([
    {
      id: '1',
      title: 'Tư vấn',
      description: 'Mô tả A',
      type: 'Loại 3',
      status: 'NEW',
      visitLocation: 'Cần Thơ',
      customerCode: 'CUST0001',
      sapCode: 'SAP0001',
      address: '32 Đường ABC, Quận 3',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Trần Thị B',
      model: 'Model A',
      dueDate: '2025-06-01',
      actualDate: '2025-07-02',
      checkInDate: '2025-05-22',
      checkOutTime: '2025-07-09',
      messageStatus: 'Lỗi',
    },
    {
      id: '2',
      title: 'Tư vấn',
      description: 'Mô tả B',
      type: 'Loại 3',
      status: 'PROCESSING',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0002',
      sapCode: 'SAP0002',
      address: '99 Đường ABC, Quận 9',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model B',
      dueDate: '2025-06-24',
      actualDate: '2025-07-11',
      checkInDate: '2025-06-02 10:19',
      checkOutTime: '2025-06-08 10:19',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '3',
      title: 'Lắp đặt',
      description: 'Mô tả C',
      type: 'Loại 2',
      status: 'NEW',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0003',
      sapCode: 'SAP0003',
      address: '4 Đường ABC, Quận 2',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Trần Thị B',
      model: 'Model C',
      dueDate: '2025-06-10',
      actualDate: '2025-06-17',
      checkInDate: '2025-07-06 10:19',
      checkOutTime: '2025-06-29 10:19',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '4',
      title: 'Kiểm tra',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'COMPLETED',
      visitLocation: 'Cần Thơ',
      customerCode: 'CUST0004',
      sapCode: 'SAP0004',
      address: '21 Đường ABC, Quận 1',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Trần Thị B',
      model: 'Model C',
      dueDate: '2025-06-23',
      actualDate: '2025-05-29',
      checkInDate: '2025-07-01 10:19',
      checkOutTime: '2025-06-04 10:19',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '5',
      title: 'Lắp đặt',
      description: 'Mô tả D',
      type: 'Loại 1',
      status: 'NEW',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0005',
      sapCode: 'SAP0005',
      address: '7 Đường ABC, Quận 4',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model B',
      dueDate: '2025-07-14',
      actualDate: '2025-06-19',
      checkInDate: '2025-05-24 10:19',
      checkOutTime: '2025-06-22 10:19',
      messageStatus: 'Đã gửi',
    },
    {
      id: '6',
      title: 'Bảo trì',
      description: 'Mô tả B',
      type: 'Loại 2',
      status: 'NEW',
      visitLocation: 'TP.HCM',
      customerCode: 'CUST0006',
      sapCode: 'SAP0006',
      address: '45 Đường ABC, Quận 7',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Phạm Thị D',
      model: 'Model A',
      dueDate: '2025-06-25',
      actualDate: '2025-06-28',
      checkInDate: '2025-06-20 08:00',
      checkOutTime: '2025-06-20 17:00',
      messageStatus: 'Đã gửi',
    },
    {
      id: '7',
      title: 'Tư vấn',
      description: 'Mô tả C',
      type: 'Loại 3',
      status: 'PROCESSING',
      visitLocation: 'Hà Nội',
      customerCode: 'CUST0007',
      sapCode: 'SAP0007',
      address: '78 Đường ABC, Quận 5',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Trần Thị B',
      model: 'Model B',
      dueDate: '2025-06-15',
      actualDate: '2025-06-17',
      checkInDate: '2025-06-15 09:30',
      checkOutTime: '2025-06-15 11:00',
      messageStatus: 'Lỗi',
    },
    {
      id: '8',
      title: 'Khảo sát',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'COMPLETED',
      visitLocation: 'Cần Thơ',
      customerCode: 'CUST0008',
      sapCode: 'SAP0008',
      address: '12 Đường ABC, Quận 2',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model C',
      dueDate: '2025-07-01',
      actualDate: '2025-07-01',
      checkInDate: '2025-07-01 08:15',
      checkOutTime: '2025-07-01 16:45',
      messageStatus: 'Đã gửi',
    },
    {
      id: '9',
      title: 'Lắp đặt',
      description: 'Mô tả D',
      type: 'Loại 3',
      status: 'NEW',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0009',
      sapCode: 'SAP0009',
      address: '5 Đường ABC, Quận 10',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Lê Văn C',
      model: 'Model B',
      dueDate: '2025-06-18',
      actualDate: '2025-06-20',
      checkInDate: '2025-06-18 13:00',
      checkOutTime: '2025-06-18 17:00',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '10',
      title: 'Bảo trì',
      description: 'Mô tả C',
      type: 'Loại 2',
      status: 'PROCESSING',
      visitLocation: 'TP.HCM',
      customerCode: 'CUST0010',
      sapCode: 'SAP0010',
      address: '67 Đường ABC, Quận 11',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Phạm Thị D',
      model: 'Model A',
      dueDate: '2025-07-05',
      actualDate: '2025-07-06',
      checkInDate: '2025-07-05 10:00',
      checkOutTime: '2025-07-05 15:30',
      messageStatus: 'Đã gửi',
    },
    {
      id: '11',
      title: 'Khảo sát',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'NEW',
      visitLocation: 'Hà Nội',
      customerCode: 'CUST0011',
      sapCode: 'SAP0011',
      address: '33 Đường ABC, Quận 6',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model A',
      dueDate: '2025-06-27',
      actualDate: '2025-06-28',
      checkInDate: '2025-06-27 08:00',
      checkOutTime: '2025-06-27 12:00',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '12',
      title: 'Tư vấn',
      description: 'Mô tả D',
      type: 'Loại 3',
      status: 'COMPLETED',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0012',
      sapCode: 'SAP0012',
      address: '88 Đường ABC, Quận 8',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Lê Văn C',
      model: 'Model C',
      dueDate: '2025-06-10',
      actualDate: '2025-06-12',
      checkInDate: '2025-06-10 14:00',
      checkOutTime: '2025-06-10 17:00',
      messageStatus: 'Lỗi',
    },
    {
      id: '13',
      title: 'Kiểm tra',
      description: 'Mô tả A',
      type: 'Loại 2',
      status: 'NEW',
      visitLocation: 'Cần Thơ',
      customerCode: 'CUST0013',
      sapCode: 'SAP0013',
      address: '22 Đường ABC, Quận 12',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Phạm Thị D',
      model: 'Model B',
      dueDate: '2025-07-07',
      actualDate: '2025-07-07',
      checkInDate: '2025-07-07 09:00',
      checkOutTime: '2025-07-07 16:00',
      messageStatus: 'Đã gửi',
    },
    {
      id: '14',
      title: 'Lắp đặt',
      description: 'Mô tả B',
      type: 'Loại 3',
      status: 'COMPLETED',
      visitLocation: 'TP.HCM',
      customerCode: 'CUST0014',
      sapCode: 'SAP0014',
      address: '19 Đường ABC, Quận 9',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Lê Văn C',
      model: 'Model A',
      dueDate: '2025-06-19',
      actualDate: '2025-06-20',
      checkInDate: '2025-06-19 07:45',
      checkOutTime: '2025-06-19 12:30',
      messageStatus: 'Đã gửi',
    },
    {
      id: '15',
      title: 'Tư vấn',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'PROCESSING',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0015',
      sapCode: 'SAP0015',
      address: '54 Đường ABC, Quận 4',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model B',
      dueDate: '2025-06-22',
      actualDate: '2025-06-23',
      checkInDate: '2025-06-22 10:00',
      checkOutTime: '2025-06-22 15:00',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '16',
      title: 'Khảo sát',
      description: 'Mô tả D',
      type: 'Loại 2',
      status: 'COMPLETED',
      visitLocation: 'Hà Nội',
      customerCode: 'CUST0016',
      sapCode: 'SAP0016',
      address: '16 Đường ABC, Quận 1',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Trần Thị B',
      model: 'Model C',
      dueDate: '2025-07-02',
      actualDate: '2025-07-03',
      checkInDate: '2025-07-02 13:00',
      checkOutTime: '2025-07-02 17:30',
      messageStatus: 'Lỗi',
    },
    {
      id: '17',
      title: 'Bảo trì',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'NEW',
      visitLocation: 'TP.HCM',
      customerCode: 'CUST0017',
      sapCode: 'SAP0017',
      address: '37 Đường ABC, Quận 3',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Phạm Thị D',
      model: 'Model B',
      dueDate: '2025-06-29',
      actualDate: '2025-06-30',
      checkInDate: '2025-06-29 08:30',
      checkOutTime: '2025-06-29 11:45',
      messageStatus: 'Chưa gửi',
    },
    {
      id: '18',
      title: 'Kiểm tra',
      description: 'Mô tả B',
      type: 'Loại 3',
      status: 'COMPLETED',
      visitLocation: 'Cần Thơ',
      customerCode: 'CUST0018',
      sapCode: 'SAP0018',
      address: '42 Đường ABC, Quận 7',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Lê Văn C',
      model: 'Model A',
      dueDate: '2025-07-06',
      actualDate: '2025-07-06',
      checkInDate: '2025-07-06 09:15',
      checkOutTime: '2025-07-06 16:00',
      messageStatus: 'Đã gửi',
    },
    {
      id: '19',
      title: 'Lắp đặt',
      description: 'Mô tả C',
      type: 'Loại 2',
      status: 'PROCESSING',
      visitLocation: 'Đà Nẵng',
      customerCode: 'CUST0019',
      sapCode: 'SAP0019',
      address: '93 Đường ABC, Quận 10',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Nguyễn Văn A',
      model: 'Model C',
      dueDate: '2025-06-30',
      actualDate: '2025-07-01',
      checkInDate: '2025-06-30 14:00',
      checkOutTime: '2025-06-30 17:15',
      messageStatus: 'Lỗi',
    },
    {
      id: '20',
      title: 'Tư vấn',
      description: 'Mô tả D',
      type: 'Loại 1',
      status: 'NEW',
      visitLocation: 'Hà Nội',
      customerCode: 'CUST0020',
      sapCode: 'SAP0020',
      address: '100 Đường ABC, Quận 9',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Phạm Thị D',
      model: 'Model B',
      dueDate: '2025-07-10',
      actualDate: '2025-07-11',
      checkInDate: '2025-07-10 10:00',
      checkOutTime: '2025-07-10 15:00',
      messageStatus: 'Đã gửi',
    },
    // 👇 Tiếp tục thêm 15 bản ghi nữa nếu bạn cần
  ]);

  const handleDelete = (record: any) => {
    setFakeData(fakeData.filter(item => item.id !== record.id));
    // toastService.success('Xoá thành công', 3);
  };

  const columns: ColumnsType<ICustomerContact> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 150,
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 250,
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
    },
    {
      title: 'Địa điểm ghi thăm',
      dataIndex: 'visitLocation',
      key: 'visitLocation',
      width: 150,
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
    },
    {
      title: 'Mã SAP',
      dataIndex: 'sapCode',
      key: 'sapCode',
      width: 120,
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 200,
    },
    {
      title: 'NV theo dõi giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 150,
    },
    {
      title: 'NV được phân công',
      dataIndex: 'assignedEmployee',
      key: 'assignedEmployee',
      width: 150,
    },
    {
      title: 'Mô đệ',
      dataIndex: 'model',
      key: 'model',
      width: 100,
    },
    {
      title: 'Ngày đến hạn',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY'),
      width: 130,
    },
    {
      title: 'Ngày TH thực tế',
      dataIndex: 'actualDate',
      key: 'actualDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY'),
      width: 130,
    },
    {
      title: 'Ngày check in',
      dataIndex: 'checkInDate',
      key: 'checkInDate',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY HH:mm'),
      width: 150,
    },
    {
      title: 'Thời gian check out',
      dataIndex: 'checkOutTime',
      key: 'checkOutTime',
      render: (value: string) => dayjs(value).format('DD/MM/YYYY HH:mm'),
      width: 150,
    },
    {
      title: 'Trạng thái gửi tin',
      dataIndex: 'messageStatus',
      key: 'messageStatus',
      width: 150,
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type="primary"
            shape="circle"
            icon={<DeleteOutlined />}
            tooltip="Delete"
            onClick={() => handleDelete(record)}
          />
        </>
      ),
    },
  ];

  return (
    <BaseCard
      title="Danh sách thăm hỏi khách hàng"
      buttons={[
        {
          text: 'Thêm mới thăm hỏi',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCustomerSupportModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => setVisibleCreateModal(false)}
        />
      )}
      <BaseView>
        <Row gutter={16}>
          <Col span={24}>
            <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} customerData={fakeData} />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24} style={{ marginTop: 16 }}>
            <BaseTable columns={columns} data={fakeData} total={0} isLoading={false} scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </BaseView>
    </BaseCard>
  );
};
