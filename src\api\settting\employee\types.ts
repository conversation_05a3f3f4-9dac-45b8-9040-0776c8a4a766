export interface IEmployee {
  id: string;
  employeeCode: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  avatar: string;
  status: string;
  note: string;
}

export interface IEmployeeFilter {
  id: string;
  employeeCode: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  avatar: string;
  status: string;
  note: string;
  pageIndex: number;
  pageSize: number;
}

export interface IEmployeeResponse {
  data: IEmployee[];
  total: number;
}
