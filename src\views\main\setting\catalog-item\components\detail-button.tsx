import { EyeOutlined } from '@ant-design/icons';
import { Card, Col, Row, Tag, Typography } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import { formatDateCustom } from '~/common/helpers/helper';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';

const { Text } = Typography;

const mockCatalogDetail = {
  id: '1',
  code: 'SP001',
  name: 'Sản phẩm A',
  type: 'PRODUCT',
  unitPrice: 100000,
  currency: 'VND',
  status: 'ACTIVE',
  createdDate: new Date().toISOString(),
  description:
    'Đây là mô tả chi tiết về sản phẩm A. Sản phẩm có chất lượng cao và được sử dụng rộng rãi trong nhiều lĩnh vực.',
};

const DetailButton = ({ id }) => {
  const [open, setOpen] = useState(false);

  const openModal = () => {
    setOpen(true);
  };

  const closeModal = () => {
    setOpen(false);
  };

  // Giả lập lấy dữ liệu dựa trên id
  const getCatalogDetail = catalogId => {
    // Trong thực tế, đây sẽ là nơi lọc dữ liệu dựa trên ID
    // Hiện tại chỉ trả về dữ liệu giả
    console.log(`Đang xem chi tiết sản phẩm có ID: ${catalogId}`);
    return mockCatalogDetail;
  };

  const data = getCatalogDetail(id);

  const modalContent = data && (
    <div>
      {/* Product Overview Card */}
      <Card style={{ marginBottom: '16px' }} size="small">
        <Row gutter={16}>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Mã sản phẩm:</Text>
            <br />
            <Text>{data.code}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tên sản phẩm:</Text>
            <br />
            <Text>{data.name}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Loại sản phẩm:</Text>
            <br />
            <Text>{NSCatalog.EType[data.type].name}</Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Đơn giá:</Text>
            <br />
            <Text>
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: data?.currency || 'VND',
              }).format(data?.unitPrice || 0)}
            </Text>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Tag color={data.status === NSCatalog.EStatus.ACTIVE.code ? 'green' : 'red'}>
              {NSCatalog.EStatus[data.status].name}
            </Tag>
          </Col>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày tạo:</Text>
            <br />
            <Text>{formatDateCustom(data.createdDate, 'DD/MM/YYYY')}</Text>
          </Col>
          <Col span={24} style={{ marginBottom: 16 }}>
            <Text strong>Mô tả:</Text>
            <br />
            <TextArea
              placeholder="Không có mô tả cho sản phẩm này"
              rows={4}
              value={data.description}
              disabled
              style={{ background: 'transparent', color: '#000', marginTop: 8 }}
            >
            </TextArea>
          </Col>
        </Row>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton
        icon={<EyeOutlined />}
        onClick={openModal}
        type="primary"
        tooltip="Xem chi tiết"
        children="Xem chi tiết"
      />
      <BaseModal
        open={open}
        title="Chi tiết sản phẩm"
        description="Thông tin chi tiết sản phẩm"
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  );
};

export default DetailButton;
