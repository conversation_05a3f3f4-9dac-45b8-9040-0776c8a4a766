import { CloseOutlined, DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Card, Form, notification, Switch } from 'antd';
import { useEffect, useState } from 'react';
import BaseConfirmButton from '~/components/base-confirm-button';
import BaseTable from '~/components/base-table';
import { useModal } from '~/hooks/use-modal';
import { ModalCreateCustomerAddress } from '../modal/modal-create-customer-address';
import { ModalEditCustomerAddress } from '../modal/modal-edit-customer-address';
import useCustomerAddress from '../../../Hooks/useCustomerAddress';
import { ICustomer } from '~/api/customer/types';
import { CustomerAddressItemDto, CustomerAddressListDto, ListCustomerAddressDto } from '~/api/customer-address/types';

type IProps = {
  onClose: () => void;
  customerData: ICustomer;
};

export const CustomerAddressTab = (props: IProps) => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const { customerData } = props;

  // Mock data cho UI
  const [addressData, setAddressData] = useState([]);
  const [total, setTotal] = useState(0);

  const { open: openEdit, openModal: openEditModal, closeModal: closeEditModal } = useModal();
  const { open: openCreate, openModal: openCreateModal, closeModal: closeCreateModal } = useModal();
  const [editData, setEditData] = useState<any>(null);

  const { listCustomerAddress, createCustomerAddress, setActiveCustomerAddress, updateCustomerAddress } =
    useCustomerAddress();

  useEffect(() => {
    loadData({});
  }, []);

  const loadData = (params: ListCustomerAddressDto) => {
    setIsLoading(true);
    listCustomerAddress({
      customerId: customerData.id,
      isActive: true,
      ...params,
    })
      .then((res: any) => {
        setAddressData(res.data);
        setTotal(res.total);
        setIsLoading(false);
      })
      .catch(err => {
        setIsLoading(false);
      });
  };
  const handleOpenEditModal = (data: any) => {
    openEditModal();
    setEditData(data);
  };

  const handleOpenCreateModal = () => {
    openCreateModal();
  };

  const handleSetActive = async (id: string) => {
    // Giả lập xóa địa chỉ
    setIsLoading(true);
    setActiveCustomerAddress(id)
      .then((res: any) => {
        if (res) {
          notification.success({
            message: res.message,
            placement: 'top',
          });
          setIsLoading(false);
          loadData({});
        }
      })
      .catch(err => {
        notification.error({
          message: 'Có lỗi xảy ra',
          description: err.message,
          placement: 'top',
        });
        setIsLoading(false);
        loadData({});
      });
    setIsLoading(false);
  };

  const handleUpdate = async (values: any) => {
    // Giả lập cập nhật địa chỉ
    setIsLoading(true);
    updateCustomerAddress(values)
      .then((res: any) => {
        if (res) {
          notification.success({
            message: res.message,
          });
          setIsLoading(false);
          closeEditModal();
          loadData({});
        }
      })
      .catch(err => {
        notification.error({
          message: 'Có lỗi xảy ra',
          description: err.message,
        });
        setIsLoading(false);
        loadData({});
      });
    closeEditModal();
  };

  const handleCreate = async (values: any) => {
    // Giả lập thêm mới địa chỉ
    setIsLoading(true);
    createCustomerAddress(values)
      .then((res: any) => {
        if (res) {
          notification.success({
            message: res.message,
          });
          setIsLoading(false);
          closeCreateModal();
          loadData({});
        }
      })
      .catch(err => {
        notification.error({
          message: 'Có lỗi xảy ra',
          description: err.message,
        });
        setIsLoading(false);
        loadData({});
      });
    closeCreateModal();
  };

  const columns: any = [
    {
      title: 'STT',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Tên địa chỉ',
      dataIndex: 'addressName',
      key: 'addressName',
      width: 200,
      align: 'center',
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 150,
      align: 'center',
    },
    {
      title: 'Địa chỉ chính',
      dataIndex: 'isDefault',
      key: 'isDefault',
      width: 120,
      align: 'center',
      render: (value: boolean) => <Switch checked={value} disabled />,
    },
    {
      title: 'Ghi chú',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      align: 'center',
    },
    {
      title: 'Tác vụ',
      key: 'actions',
      width: 100,
      align: 'center',
      render: (_, record: any) => (
        <div style={{ display: 'flex', gap: 8, justifyContent: 'center' }}>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              handleOpenEditModal(record);
            }}
            size="middle"
          />

          <BaseConfirmButton
            icon={<DeleteOutlined />}
            tooltip="Delete"
            danger
            confirmTitle="Bạn có chắc muốn xóa địa chỉ này không?"
            onConfirm={() => {
              handleSetActive(record.id);
            }}
            size="middle"
            type="default"
          />
        </div>
      ),
    },
  ];

  const handlePageChange = (pageIndex: number, pageSize: number) => {
    loadData({
      pageIndex,
      pageSize,
    });
  };

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={() => {}}>
      <Card
        title="Thông tin địa chỉ khách hàng"
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={handleOpenCreateModal}>
            Thêm mới
          </Button>
        }
      >
        <BaseTable
          columns={columns}
          data={addressData}
          total={total}
          isLoading={isLoading}
          onPageChange={handlePageChange}
        />
      </Card>
      <ModalEditCustomerAddress
        open={openEdit}
        onClose={closeEditModal}
        data={editData}
        onSave={handleUpdate}
        style={{ width: 800 }}
      />

      <ModalCreateCustomerAddress
        open={openCreate}
        onClose={closeCreateModal}
        onSave={handleCreate}
        customerId={customerData.id}
        style={{ width: 800 }}
      />
      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Button
          danger
          htmlType="button"
          icon={<CloseOutlined style={{ fontSize: 12 }} />}
          style={{ marginRight: 8 }}
          onClick={props.onClose}
        >
          Đóng
        </Button>
      </div>
    </Form>
  );

  return modalContent;
};
