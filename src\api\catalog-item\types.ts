import { IPageRequest, IPageResponse } from '../@common/types';

export enum ECatalogItemStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
}

export interface ICreateCatalogItem {
  code: string;
  name: string;
  description?: string | null;
  images?: string | null;
  attachments?: string | null;
  status: ECatalogItemStatus;
}

export interface IUpdateCatalogItem extends Partial<ICreateCatalogItem> {
  id: string;
}

export interface IListCatalogItem extends IPageRequest {
  q?: string;
  name?: string;
  status?: ECatalogItemStatus;
  createdFrom?: string;
  createdTo?: string;
}

export interface ICatalogItem extends ICreateCatalogItem {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface IListCatalogItemResponse extends IPageResponse<ICatalogItem> {}
