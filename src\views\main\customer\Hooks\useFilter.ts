import { useState } from 'react';
import { enumData } from '~/common/enums/enumData';

const useFilter = (employees: any[]) => {
  const [filterData, setFilterData] = useState<any>({});
  const [selectedStage, setSelectedStage] = useState<string | null>(null);

  const filterFields = [
    { key: 'code', name: '<PERSON><PERSON> khách hàng', type: enumData.FILTER_TYPE.INPUT.key },
    {
      key: 'name',
      name: 'Tên khách hàng',
      type: enumData.FILTER_TYPE.INPUT.key,
    },
    {
      key: 'phone',
      name: '<PERSON><PERSON> điện thoại',
      type: enumData.FILTER_TYPE.INPUT.key,
    },
    {
      key: 'salesRep',
      name: '<PERSON>h<PERSON> viên phụ trách',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: employees.map(emp => ({ value: emp.code, name: emp.name })),
    },
    {
      key: 'address',
      name: 'Địa chỉ',
      type: enumData.FILTER_TYPE.INPUT.key,
    },
    {
      key: 'provinceCode',
      name: 'Tỉnh/Thành phố',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: [
        { value: 'HCM', name: 'TP. Hồ Chí Minh' },
        { value: 'HN', name: 'Hà Nội' },
        { value: 'DN', name: 'Đà Nẵng' },
      ],
    },
    {
      key: 'districtCode',
      name: 'Quận/Huyện',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: [
        { value: 'Q1', name: 'Quận 1' },
        { value: 'Q2', name: 'Quận 2' },
        { value: 'Q3', name: 'Quận 3' },
      ],
    },
    {
      key: 'wardCode',
      name: 'Phường/Xã',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: [
        { value: 'P1', name: 'Phường 1' },
        { value: 'P2', name: 'Phường 2' },
        { value: 'P3', name: 'Phường 3' },
      ],
    },
    {
      key: 'source',
      name: 'Nguồn khách hàng',
      type: enumData.FILTER_TYPE.SELECT.key,
      selectOptions: [
        { value: 'ONLINE', name: 'Trực tuyến' },
        { value: 'REFERRAL', name: 'Giới thiệu' },
        { value: 'DIRECT', name: 'Trực tiếp' },
      ],
    },
    {
      key: 'createdDate',
      name: 'Ngày tạo',
      type: 'date',
    },
    {
      key: 'createdBy',
      name: 'Người tạo',
      type: enumData.FILTER_TYPE.INPUT.key,
    },
  ];

  const handleFilter = (values: any) => {
    console.log('Filter values:', values);
    setFilterData(values);
  };

  const handleFilterReset = () => {
    setFilterData({});
    setSelectedStage(null);
  };

  return {
    filterData,
    setFilterData,
    filterFields,
    handleFilter,
    handleFilterReset,
    selectedStage,
    setSelectedStage,
  };
};

export default useFilter;
