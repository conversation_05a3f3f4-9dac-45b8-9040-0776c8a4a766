import { SaveOutlined } from '@ant-design/icons';
import { Button, Form, Input, Row, Col, Drawer, Select, message } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import { ICatalog } from '~/api/catalog/types';
import { useCatalog } from '../hook/useCatalog';

const { Option } = Select;
const { TextArea } = Input;

interface UpdateItemModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  width?: number | string;
  item: ICatalog;
}

// Dữ liệu giả cho danh mục
const mockCatalogs: Array<{
  id: string;
  code: string;
  name: string;
  description?: string;
  status: string;
}> = [];

const UpdateCatalogModal = ({ open, onClose, onSuccess, width = 600, item }: UpdateItemModalProps): JSX.Element => {
  const [form] = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { update } = useCatalog();

  const handleUpdate = async (values: any) => {
    if (!values) return;
    setIsSubmitting(true);
    try {
      await update({ ...values, id: item.id });
      message.success('Cập nhật danh mục thành công');
      onClose();
      if (onSuccess) onSuccess();
    } catch (error) {
      message.error('Có lỗi xảy ra khi cập nhật danh mục');
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
    form.setFieldsValue(item);
  }, [open, form, item]);

  return (
    <Drawer
      title="Cập nhật danh mục"
      width={width}
      onClose={onClose}
      open={open}
      bodyStyle={{ paddingBottom: 80 }}
      extra={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button onClick={() => form.submit()} type="primary" icon={<SaveOutlined />} loading={isSubmitting}>
            Lưu lại
          </Button>
        </div>
      }
    >
      <Form form={form} layout="vertical" onFinish={handleUpdate}>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="code"
              label="Mã danh mục"
              rules={[{ required: true, message: 'Vui lòng nhập mã danh mục' }]}
            >
              <Input placeholder="Nhập mã danh mục" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="name"
              label="Tên danh mục"
              rules={[{ required: true, message: 'Vui lòng nhập tên danh mục' }]}
            >
              <Input placeholder="Nhập tên danh mục" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name="description" label="Mô tả">
              <TextArea rows={4} placeholder="Nhập mô tả (nếu có)" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default UpdateCatalogModal;
