import { EditOutlined, PauseCircleOutlined, PlayCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Space, Tag, Tooltip } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import { useCatalog } from './hook/useCatalog';
import CreateCatalogModal from './components/create-catalog-modal';
import { ICatalog } from '~/api/catalog/types';
import UpdateCatalogModal from './components/update-catalog-modal';

export interface Catalog {
  id?: string;
  itemCategory?: string;
  itemGroup?: string;
  code?: string;
  name?: string;
  unit?: string;
  unitPrice?: number;
  currency?: string;
  description?: string;
  status?: string;
  createdDate?: string;
  createdBy?: string;
}

export const CatalogView = () => {
  const {
    data,
    isLoading,
    total,
    page,
    setPage,
    create,
    update,
    active,
    inactive,
  } = useCatalog();

  const [visibleCreateModal, setVisibleCreateModal] = useState(false);
  const [visibleUpdateModal, setVisibleUpdateModal] = useState(false);
  const [catalog, setCatalog] = useState<ICatalog | null>(null);
  const [filters, setFilters] = useState<{
    q?: string;
    status?: string;
    dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  }>({});

  // Handle filter changes
  const handleFilter = (values: any) => {
    const { dateRange, ...rest } = values;
    const newFilters = {
      ...rest,
      dateRange: dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : undefined,
    };

    setFilters(newFilters);
    setPage(prev => ({
      ...prev,
      pageIndex: 1, // Reset to first page when filter changes
      ...newFilters,
    }));
  };

  // Handle filter reset
  const handleFilterReset = () => {
    setFilters({});
    setPage(prev => ({
      pageIndex: 1,
      pageSize: prev.pageSize,
    }));
  };

  // Handle status change
  const handleStatusChange = (id: string, currentStatus: string) => {
    if (currentStatus === NSCatalog.EStatus.ACTIVE.code) {
      inactive(id);
    } else {
      active(id);
    }
  };

  // Handle edit
  const handleEdit = (data: ICatalog) => {
    setVisibleUpdateModal(true);
    setCatalog(data);
  };

  const columns: ColumnsType<ICatalog> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => (page.pageIndex - 1) * page.pageSize + index + 1,
    },
    {
      title: 'Mã danh mục',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên danh mục',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Miêu tả',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const isActive = status === NSCatalog.EStatus.ACTIVE.code;
        return (
          <Tag color={isActive ? 'green' : 'red'}>
            {isActive ? 'Đang hoạt động' : 'Ngừng hoạt động'}
          </Tag>
        );
      },
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdDate',
      key: 'createdDate',
      render: (date: string) => date ? new Date(date).toLocaleDateString() : '',
    },
    {
      title: 'Thao tác',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="Chỉnh sửa">
            <Button type="link" onClick={() => handleEdit(record)}>
              <EditOutlined />
            </Button>
          </Tooltip>
          <Tooltip title={record.status === NSCatalog.EStatus.ACTIVE.code ? 'Ngừng kích hoạt' : 'Kích hoạt'}>
            <Popconfirm
              title={record.status === NSCatalog.EStatus.ACTIVE.code ? 'Ngừng kích hoạt' : 'Kích hoạt'}
              onConfirm={() => handleStatusChange(record.id || '', record.status || '')}
            >
              <Button
                type="link"
                danger={record.status === NSCatalog.EStatus.ACTIVE.code}
              >
                {record.status === NSCatalog.EStatus.ACTIVE.code ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              </Button>
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Filter fields configuration
  const filterFields = [
    {
      key: 'name',
      name: 'Tên danh mục',
      type: 'input',
      placeholder: 'Nhập tên danh mục',
    },
    {
      key: 'code',
      name: 'Mã danh mục',
      type: 'input',
      placeholder: 'Nhập mã danh mục',
    },
    {
      key: 'status',
      name: 'Trạng thái',
      type: 'select',
      selectOptions: [
        { value: NSCatalog.EStatus.ACTIVE.code, name: 'Đang hoạt động' },
        { value: NSCatalog.EStatus.INACTIVE.code, name: 'Ngừng hoạt động' },
      ],
    },
    {
      key: 'dateRange',
      name: 'Ngày tạo',
      type: 'dateRange',
    },
  ];

  const onPageChange = (pageIndex: number, pageSize: number) => {
    setPage(prev => ({
      ...prev,
      pageIndex,
      pageSize,
    }));
  };

  return (
    <BaseCard
      title="Thiết lập danh mục"
      buttons={[
        {
          text: 'Thêm mới danh mục',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCatalogModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => {
            setVisibleCreateModal(false);
            // Refresh data after successful creation
            setPage(prev => ({ ...prev }));
          }}
        />
      )}
      {visibleUpdateModal && (
        <UpdateCatalogModal
          item={catalog}
          open={visibleUpdateModal}
          onClose={() => setVisibleUpdateModal(false)}
          onSuccess={() => {
            setVisibleUpdateModal(false);
            // Refresh data after successful update
            setPage(prev => ({ ...prev }));
          }}
        />
      )}
      {/* Filter */}
      <div style={{ marginBottom: 16 }}>
        <BaseFilter
          onFilter={handleFilter}
          onReset={handleFilterReset}
          filters={filterFields}
          isLoading={isLoading}
        />
      </div>
      <BaseTable
        columns={columns}
        data={data || []}
        total={total}
        isLoading={isLoading}
        defaultPageSize={page.pageSize}
        onPageChange={onPageChange}
        pagination={{
          current: page.pageIndex,
          pageSize: page.pageSize,
          total,
          showSizeChanger: true,
          showTotal: (total) => `Tổng ${total} bản ghi`,
        }}
        rowKey="id"
      />
    </BaseCard>
  );
};

export default CatalogView;
