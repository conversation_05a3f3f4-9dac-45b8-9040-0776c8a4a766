export namespace NSCustomer {
  export enum CustomerType {
    // Khách hàng chưa định danh
    NEW = 'NEW',
    // Khách hàng tiềm năng
    NURTURE = 'NURTURE',
    // Khách hàng đủ điều kiện
    QUALIFIED = 'QUALIFIED',
    // Khách hàng cơ hội
    OPPORTUNITY = 'OPPORTUNITY',
    // Khách hàng chính thức
    WON = 'WON',
    // Khách hàng chăm sóc & phát triển
    CARE = 'CARE',
    // Khách hàng không thành công (rời bỏ)
    LOST = 'LOST',
    // Khách hàng Lưu trữ
    ARCHIVED = 'ARCHIVED',
  }
  export enum CustomerStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
  }

  export enum Source {
    WEBSITE = 'WEBSITE',
    FACEBOOK = 'FACEBOOK',
    GOOGLE = 'GOOGLE',
    INTRODUCTION = 'INTRODUCTION',
    OTHER = 'OTHER',
  }
  export const MARKET = {
    // Trong nước
    DOMESTIC: {
      value: 'DOMESTIC',
      name: 'Trong nước',
    },
    // Nước ngoài
    FOREIGN: {
      value: 'FOREIGN',
      name: 'Quốc tế',
    },
  };


  export enum EMarket {
    DOMESTIC = 'DOMESTIC',
    FOREIGN = 'FOREIGN',
  }


  export enum Position {
    CEO = 'CEO', // Tổng giám đốc
    CTO = 'CTO', // Giám đốc kỹ thuật
    CMO = 'CMO', // Giám đốc marketing
    CRO = 'CRO', // Giám đốc kinh doanh
    CFO = 'CFO', // Giám đốc tài chính
    HR = 'HR', // Giám đốc nhân sự
    IT = 'IT', // Giám đốc công nghệ thông tin
    OTHER = 'OTHER', // Khác
  }
}

export const ECustomer = {
  ECustomerPosition: {
    CEO: {
      label: 'CEO',
      value: NSCustomer.Position.CEO,
      name: 'Tổng giám đốc',
    },
    CTO: {
      label: 'CTO',
      value: NSCustomer.Position.CTO,
      name: 'Giám đốc kỹ thuật',
    },

    CMO: {
      label: 'CMO',
      value: NSCustomer.Position.CMO,
      name: 'Giám đốc marketing',
    },

    CRO: {
      label: 'CRO',
      value: NSCustomer.Position.CRO,
      name: 'Giám đốc kinh doanh',
    },

    CFO: {
      label: 'CFO',
      value: NSCustomer.Position.CFO,
      name: 'Giám đốc tài chính',
    },

    HR: {
      label: 'HR',
      value: NSCustomer.Position.HR,
      name: 'Giám đốc nhân sự',
    },

    IT: {
      label: 'IT',
      value: NSCustomer.Position.IT,
      name: 'Giám đốc công nghệ thông tin',
    },

    OTHER: {
      label: 'OTHER',
      value: NSCustomer.Position.OTHER,
      name: 'Khác',
    },
  },
  ECustomerStatus: {
    ACTIVE: {
      label: 'Active',
      value: NSCustomer.CustomerStatus.ACTIVE,
      name: 'Hoạt động',
      color: 'green',
    },
    INACTIVE: {
      label: 'Inactive',
      value: NSCustomer.CustomerStatus.INACTIVE,
      name: 'Không hoạt động',
      color: 'red',
    },
  },
  ECustomerType: {
    NEW: {
      label: 'New',
      value: 'NEW',
      name: 'Khách hàng chưa định danh',
      color: '#00ccff',
      description: 'Chưa có đầy đủ thông tin cơ bản',
    },
    NURTURE: {
      label: 'Nurture',
      value: 'NURTURE',
      name: 'Khách hàng tiềm năng',
      color: '#ffff00ff',
      description: 'Đã có tương tác sơ bộ, đủ điều kiện marketing chăm sóc',
    },
    QUALIFIED: {
      label: 'Qualified',
      value: 'QUALIFIED',
      name: 'Khách hàng đủ điều kiện',
      color: '#000066',
      description: 'Đang đánh giá / Xác minh nhu cầu. Có nhu cầu thật, người ra quyết định, khả năng mua.',
    },
    OPPORTUNITY: {
      label: 'Opportunity',
      value: 'OPPORTUNITY',
      name: 'Khách hàng cơ hội',
      color: '#ff9900',
      description: 'Đang trong quá trình tư vấn, demo, đàm phán, gửi báo giá',
    },
    WON: {
      label: 'Won',
      value: 'WON',
      name: 'Khách hàng chính thức',
      color: '#0066ff',
      description: 'Đã ký hợp đồng, đang triển khai hoặc sử dụng dịch vụ ',
    },
    LOST: {
      label: 'Lost',
      value: 'LOST',
      name: 'Khách hàng không thành công',
      color: '#ff3300',
      description: 'Đã rời đi, mất hợp đồng hoặc không còn tương tác dài hạn',
    },
    CARE: {
      label: 'Care',
      value: 'CARE',
      name: 'Chăm sóc & phát triển',
      color: '#009900',
      description: 'Khách hàng lớn, đang khai thác thêm cơ hội bán hàng',
    },
  },
  ECustomerSource: {
    WEBSITE: {
      label: 'Website',
      value: 'WEBSITE',
      name: 'Website',
    },
    FACEBOOK: {
      label: 'Facebook',
      value: 'FACEBOOK',
      name: 'Facebook',
    },
    GOOGLE: {
      label: 'Google',
      value: 'GOOGLE',
      name: 'Google',
    },
    REFERRAL: {
      label: 'Referral',
      value: 'REFERRAL',
      name: 'Referral',
    },
    OTHER: {
      label: 'Other',
      value: 'OTHER',
      name: 'Other',
    },
  },
  ECustomerMarket: {
    DOMESTIC: {
      label: 'Domestic',
      value: 'DOMESTIC',
      name: 'Trong nước',
    },
    INTERNATIONAL: {
      label: 'International',
      value: 'INTERNATIONAL',
      name: 'Quốc tế',
    },
  },
};
