{"compilerOptions": {"target": "ESNext", "jsx": "react-jsx", "baseUrl": "./", "module": "ESNext", "moduleResolution": "bundler", "paths": {"~/*": ["./src/*"]}, "resolveJsonModule": true, "types": ["vitest/globals", "@testing-library/jest-dom"], "allowJs": true, "strict": false, "noFallthroughCasesInSwitch": true, "importHelpers": false, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["src", "fake", "tests", "vite.config.ts", "commitlint.config.ts"]}