# Stage 1 - Build với Bun
FROM node:22.12-alpine AS build-deps
WORKDIR /usr/src/app

# Cài Bun
RUN apk add --no-cache curl bash \
    && curl -fsSL https://bun.sh/install | bash \
    && mv /root/.bun/bin/bun /usr/local/bin/bun

# Copy package files trước để cache install
COPY package.json bun.lockb* ./

# Cài deps bằng Bun
RUN bun install --frozen-lockfile

# Copy toàn bộ source code
COPY . ./

ARG VITE_ROOT_API_BASE_URL="https://crm-api-dev.apetechs.co"
ARG VITE_BASE_HOME_PATH="/home"
ARG VITE_GLOB_APP_TITLE="CRM"
ARG VITE_APE_SSO_URL='https://authenticator-dev.apetechs.co'
ARG VITE_APE_CLIENT_ID='CRM_f12880477d2f44eab09d0c1f6943ce61'
ARG VITE_APE_CLIENT_SECRET='0527af904cf9842984a10bd013c71237c9960a9f1a651d2dbad5261c0456c80b'
ARG VITE_APE_CALLBACK_URL="https://crm-api-dev.apetechs.co/api/client/auth/ape/callback"

ENV VITE_ROOT_API_BASE_URL=$VITE_ROOT_API_BASE_URL \
    VITE_BASE_HOME_PATH=$VITE_BASE_HOME_PATH \
    VITE_GLOB_APP_TITLE=$VITE_GLOB_APP_TITLE \
    VITE_APE_CLIENT_ID=$VITE_APE_CLIENT_ID \
    VITE_APE_CLIENT_SECRET=$VITE_APE_CLIENT_SECRET \
    VITE_APE_CALLBACK_URL=$VITE_APE_CALLBACK_URL \
    VITE_APE_SSO_URL=$VITE_APE_SSO_URL


# Build với Vite (qua script build)
RUN bun run build

# Stage 2 - Serve với Nginx
FROM nginx:1.23-alpine

# Copy static files từ stage build
COPY --from=build-deps /usr/src/app/build /usr/share/nginx/html

# Thay nginx.conf custom
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
