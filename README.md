**English**

## Introduction

react-antd-admin is a middle and back-office solution based on React Hooks, Vite, and TypeScript. It aims to help you quickly build enterprise-level middle and back-office projects, with no additional configuration required, ready to use out of the box.

## Features

- Cutting-edge technology stack: [React Hooks](https://react.dev/)、[TypeScript](https://www.typescriptlang.org/)、[Vite](https://vitejs.dev/)、[ant design](https://ant.design/)、[React Router](https://reactrouter.com/)、[Tailwind CSS](https://tailwindcss.com/docs/installation)
- Intuitive state management library: [Zustand](https://zustand-demo.pmnd.rs/)
- Internationalization: [I18n](https://react.i18next.com/)
- Fetch requests: [Ky](https://github.com/sindresorhus/ky)、[@tanstack/react-query](https://tanstack.com/query/latest/docs/framework/react/overview)
- Code formatting: [ESLint Flat Config](https://eslint.org/docs/latest/use/configure/configuration-files-new/)
- Route-level component caching: [keepalive-for-react](https://github.com/irychen/keepalive-for-react)
- API Mocking: [vite-plugin-fake-server](https://github.com/condorheroblog/vite-plugin-fake-server)
- Permission Routing: Supports both frontend static routing and backend dynamic routing
- Theme Configuration: Built-in multiple theme configurations, supports dark theme, and unified color system for Ant Design and Tailwind CSS

## Preview

[react-antd-admin](https://condorheroblog.github.io/react-antd-admin/)

## Documentation

[react-antd-admin Documentation](https://condorheroblog.github.io/react-antd-admin/docs/)

## Usage

### GitHub Template

[Create a repository using this template](https://github.com/new?template_name=react-antd-admin&template_owner=condorheroblog)

## Development

### Install

```bash
bun install
```

### Run

```bash
bun dev
```

Open your browser and enter [http://localhost:3333](http://localhost:3333) to see the page.

## Build

```bash
bun run build
```

The build output is by default in the build folder.

## Preview

```bash
bun run preview
```

## Credits
