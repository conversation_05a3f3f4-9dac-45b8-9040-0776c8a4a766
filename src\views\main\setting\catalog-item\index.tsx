import { PlayCircleOutlined, PlusOutlined, StopOutlined } from '@ant-design/icons';
import { Space, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseConfirmButton from '~/components/base-confirm-button';

import BaseTable from '~/components/base-table';

import BaseCard from '~/components/base-card';
import BaseFilter from '~/components/base-filter/BaseFilter';
import CreateCatalogModal from './components/create-catalog-modal';
import DetailButton from './components/detail-button';
import EditButton from './components/edit-button';

export interface CatalogItem {
  id?: string;
  itemCategory?: string;
  itemGroup?: string;
  code?: string;
  name?: string;
  unit?: string;
  unitPrice?: number;
  currency?: string;
  description?: string;
  status?: string;
  createdDate?: string;
  createdBy?: string;
}

// Dữ liệu giả cho danh sách sản phẩm
const mockCatalogs: CatalogItem[] = [
  {
    id: '1',
    code: 'SP001',
    name: 'Sản phẩm A',
    unit: 'PIECE',
    unitPrice: 100000,
    currency: 'VND',
    description: 'Mô tả sản phẩm A',
    status: 'ACTIVE',
    createdDate: new Date().toISOString(),
    createdBy: 'Admin',
  },
  {
    id: '2',
    code: 'SP002',
    name: 'Sản phẩm B',
    unit: 'BOX',
    unitPrice: 200000,
    currency: 'VND',
    description: 'Mô tả sản phẩm B',
    status: 'ACTIVE',
    createdDate: new Date(Date.now() - 86400000).toISOString(),
    createdBy: 'Admin',
  },
  {
    id: '3',
    code: 'SP003',
    name: 'Sản phẩm C',
    unit: 'PIECE',
    unitPrice: 150000,
    currency: 'VND',
    description: 'Mô tả sản phẩm C',
    status: 'INACTIVE',
    createdDate: new Date(Date.now() - 172800000).toISOString(),
    createdBy: 'Admin',
  },
];

// Dữ liệu giả cho bộ lọc
const mockFilterFields = [
  {
    key: 'code',
    name: 'Mã sản phẩm',
    type: 'INPUT',
  },
  {
    key: 'name',
    name: 'Tên sản phẩm',
    type: 'INPUT',
  },
  {
    key: 'status',
    name: 'Trạng thái',
    type: 'SELECT',
    selectOptions: [
      { value: 'ACTIVE', name: 'Đang hoạt động' },
      { value: 'INACTIVE', name: 'Ngưng hoạt động' },
    ],
  },
];

export const CatalogView = () => {
  const [data, setData] = useState<CatalogItem[]>(mockCatalogs);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(mockCatalogs.length);
  const [visibleCreateModal, setVisibleCreateModal] = useState(false);
  const [filterData, setFilterData] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  // Hàm xử lý lọc dữ liệu
  const handleFilter = (values: any) => {
    setIsLoading(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      let filteredData = [...mockCatalogs];

      // Lọc theo các điều kiện
      if (values.code) {
        filteredData = filteredData.filter(item => item.code?.toLowerCase().includes(values.code.toLowerCase()));
      }

      if (values.name) {
        filteredData = filteredData.filter(item => item.name?.toLowerCase().includes(values.name.toLowerCase()));
      }

      if (values.status) {
        filteredData = filteredData.filter(item => item.status === values.status);
      }

      setData(filteredData);
      setTotal(filteredData.length);
      setIsLoading(false);
    }, 500);
  };

  // Hàm reset bộ lọc
  const handleFilterReset = () => {
    setIsLoading(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      setData(mockCatalogs);
      setTotal(mockCatalogs.length);
      setIsLoading(false);
    }, 300);
  };

  // Hàm xử lý kích hoạt/vô hiệu hóa sản phẩm
  const handleSetActiveStatus = (id: string | undefined, newStatus: string) => {
    if (!id) return;

    setIsLoading(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      const updatedData = data.map(item => {
        if (item.id === id) {
          return { ...item, status: newStatus };
        }
        return item;
      });

      setData(updatedData);

      // Cập nhật dữ liệu gốc
      const index = mockCatalogs.findIndex(item => item.id === id);
      if (index !== -1) {
        mockCatalogs[index].status = newStatus;
      }

      setIsLoading(false);
    }, 500);
  };

  const columns: ColumnsType<CatalogItem> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const color = status === NSCatalog.EStatus.ACTIVE.code ? 'green' : 'red';
        const label = status === NSCatalog.EStatus.ACTIVE.code ? 'Đang hoạt động' : 'Ngưng hoạt động';

        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (_, record) => (
        <Space>
          <DetailButton id={record.id} />
          <EditButton data={record} onSuccess={() => handleFilterReset()} />
          <BaseConfirmButton
            icon={record.status === NSCatalog.EStatus.ACTIVE.code ? <StopOutlined /> : <PlayCircleOutlined />}
            tooltip={record.status === NSCatalog.EStatus.ACTIVE.code ? 'Ngưng hoạt động' : 'Hoạt động lại'}
            confirmTitle={
              record.status === NSCatalog.EStatus.ACTIVE.code
                ? 'Bạn có chắc muốn ngưng hoạt động sản phẩm này?'
                : 'Bạn có chắc muốn hoạt động lại sản phẩm này?'
            }
            type="default"
            danger={record.status === NSCatalog.EStatus.ACTIVE.code}
            onConfirm={() => {
              if (record.status === NSCatalog.EStatus.ACTIVE.code) {
                handleSetActiveStatus(record.id, NSCatalog.EStatus.INACTIVE.code);
              } else {
                handleSetActiveStatus(record.id, NSCatalog.EStatus.ACTIVE.code);
              }
            }}
          />
        </Space>
      ),
    },
  ];

  const onPageChange = (newPageIndex: number, newPageSize: number) => {
    setFilterData({
      ...filterData,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  return (
    <BaseCard
      title="Thiết lập sản phẩm"
      buttons={[
        {
          text: 'Thêm mới sản phẩm',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateCatalogModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => setVisibleCreateModal(false)}
        />
      )}
      {/* Bộ lọc */}
      <BaseFilter
        onFilter={handleFilter}
        onReset={handleFilterReset}
        isLoading={isLoading}
        filters={mockFilterFields}
      />
      <BaseTable
        columns={columns}
        data={data || []}
        total={total || 0}
        isLoading={isLoading}
        onPageChange={onPageChange}
      />
    </BaseCard>
  );
};

export default CatalogView;
