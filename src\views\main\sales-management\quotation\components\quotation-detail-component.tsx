import { Card, Col, Descriptions, Divider, Row, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useSearchParams } from 'react-router';

import { formatMoneyVND } from '~/common/helpers/helper';
import BaseTable from '~/components/base-table';

const { Text } = Typography;

// Danh sách chi tiết dịch vụ
const serviceData = [
  {
    key: '1',
    stt: 1,
    service: 'Phí vận chuyển cơ bản',
    description: 'Từ Hà Nội đến TP.HCM',
    unit: 'Chuyến',
    quantity: 1,
    unitPrice: 1500000,
    amount: 1500000,
  },
  {
    key: '2',
    stt: 2,
    service: 'Phí đóng gói',
    description: 'Bao bì, kiện hàng',
    unit: 'Gói',
    quantity: 1,
    unitPrice: 200000,
    amount: 200000,
  },
  {
    key: '3',
    stt: 3,
    service: '<PERSON><PERSON> bảo hiểm',
    description: 'Hàng hóa trong quá trình vận chuyển',
    unit: 'Gói',
    quantity: 1,
    unitPrice: 100000,
    amount: 100000,
  },
  {
    key: '4',
    stt: 4,
    service: 'Phí lưu kho',
    description: 'Tại điểm trung chuyển',
    unit: 'Ngày',
    quantity: 1,
    unitPrice: 50000,
    amount: 50000,
  },
];

// Tính tổng phí
const total = serviceData.reduce((sum, item) => sum + item.amount, 0);
const vat = total * 0.1;
const grandTotal = total + vat;

// Dữ liệu mẫu cho báo giá
const mockQuotationDetail = {
  id: '1',
  quotationNumber: 'BG-2023-001',
  quotationDate: '2023-10-15',
  companyName: 'Công ty TNHH Apetechs',
  companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
  companyTaxCode: '0123456789',
  companyContactPerson: 'Nguyễn Văn A',
  companyPhone: '0901234567',
  companyEmail: '<EMAIL>',
  customerName: 'Công ty TNHH ABC',
  customerAddress: '456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
  customerTaxCode: '0987654321',
  customerContactPerson: 'Trần Thị B',
  customerPhone: '0909876543',
  deliveryDate: '2023-10-30',
  deliveryLocation: 'Kho hàng Công ty ABC, 456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
  paymentMethod: 'Chuyển khoản ngân hàng',
  validityDays: 30,
  totalAmount: 2035000,
  notes:
    'Báo giá có hiệu lực trong vòng 30 ngày kể từ ngày phát hành. Vui lòng liên hệ với chúng tôi nếu có bất kỳ thắc mắc nào.',
  quoteItems: [
    {
      id: '1',
      code: 'SP001',
      name: 'Máy tính xách tay Dell XPS 13',
      unit: 'Chiếc',
      quantity: 2,
      unitPrice: 25000000,
      totalBeforeVat: 50000000,
      vat: 10,
      totalAfterVat: 55000000,
    },
    {
      id: '2',
      code: 'SP002',
      name: 'Màn hình Dell 27 inch',
      unit: 'Chiếc',
      quantity: 3,
      unitPrice: 5000000,
      totalBeforeVat: 15000000,
      vat: 10,
      totalAfterVat: 16500000,
    },
    {
      id: '3',
      code: 'SP003',
      name: 'Bàn phím cơ Logitech',
      unit: 'Chiếc',
      quantity: 5,
      unitPrice: 2000000,
      totalBeforeVat: 10000000,
      vat: 10,
      totalAfterVat: 11000000,
    },
  ],
};

export const QuotationDetailComponent = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(mockQuotationDetail);

  // Giả lập việc lấy dữ liệu
  const getQuotationDetail = (id: string | null) => {
    if (!id) return null;
    setIsLoading(true);
    // Giả lập thời gian tải dữ liệu
    setTimeout(() => {
      setData(mockQuotationDetail);
      setIsLoading(false);
    }, 500);
  };

  // Giả lập việc làm mới dữ liệu
  const refetch = () => {
    getQuotationDetail(id);
  };

  if (!id) return null;

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'totalBeforeVat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'VAT (%)',
      dataIndex: 'vat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Thành tiền sau VAT',
      dataIndex: 'totalAfterVat',
      render: value => formatMoneyVND(value),
    },
  ];

  return (
    <Card title={`Chi tiết báo giá [${data?.quotationNumber}]`}>
      <Card title="Công ty">
        <Row gutter={16}>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Tên công ty">{data?.companyName}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Địa chỉ">{data?.companyAddress}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Mã số thuế">{data?.companyTaxCode}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Người liên hệ">{data?.companyContactPerson}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Điện thoại">{data?.companyPhone}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Email">{data?.companyEmail}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Số báo giá">{data?.quotationNumber}</Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Ngày báo giá">
                {dayjs(data?.quotationDate).format('DD/MM/YYYY')}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card title="Thông tin khách hàng">
        <Row gutter={16}>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Khách hàng">{data?.customerName}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Địa chỉ">{data?.customerAddress}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Mã số thuế">{data?.customerTaxCode}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={16}>
            <Descriptions>
              <Descriptions.Item label="Người liên hệ">{data?.customerContactPerson}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Điện thoại">{data?.customerPhone}</Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
      <Divider />
      <BaseTable
        columns={columns}
        data={data?.quoteItems || []}
        total={data?.quoteItems?.length || 0}
        isLoading={isLoading}
        pagination={false}
        scroll={{ x: 'max-content' }}
      />
      <Divider />
      <Card title="Điều kiện báo giá">
        <Row gutter={16}>
          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Thời gian giao hàng">
                {dayjs(data?.deliveryDate).format('DD/MM/YYYY')}
              </Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Địa điểm giao hàng">{data?.deliveryLocation}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Phương thức thanh toán">{data?.paymentMethod}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Hiệu lực báo giá (ngày)">{data?.validityDays}</Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={8}>
            <Descriptions>
              <Descriptions.Item label="Tổng trị giá báo giá">
                {formatMoneyVND(Number(data?.totalAmount))}
              </Descriptions.Item>
            </Descriptions>
          </Col>

          <Col span={24}>
            <Descriptions>
              <Descriptions.Item label="Ghi chú">{data?.notes}</Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
    </Card>
  );
};
