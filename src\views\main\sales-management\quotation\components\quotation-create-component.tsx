import { SaveOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import { <PERSON><PERSON>, Card, Col, DatePicker, Divider, Form, Input, InputNumber, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { Table } from 'antd/lib';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { formatMoneyVND } from '~/common/helpers/helper';
import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';

const { Option } = Select;

interface CreateQuotationModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// Dữ liệu giả cho danh sách khách hàng
const mockCustomers = [
  {
    value: '1',
    label: 'Công ty TNHH ABC',
    customerAddress: '123 Đường A, Quận 1, TP.HCM',
    customerTaxCode: '0301234567',
    contacts: [
      { value: '1', label: 'Nguyễn Văn X', phone: '0901234567' },
      { value: '2', label: 'Trần Thị Y', phone: '0912345678' },
    ],
  },
  {
    value: '2',
    label: 'Công ty CP XYZ',
    customerAddress: '456 Đường B, Quận 2, TP.HCM',
    customerTaxCode: '0309876543',
    contacts: [
      { value: '3', label: 'Lê Văn Z', phone: '0923456789' },
      { value: '4', label: 'Phạm Thị W', phone: '0934567890' },
    ],
  },
  {
    value: '3',
    label: 'Công ty TNHH DEF',
    customerAddress: '789 Đường C, Quận 3, TP.HCM',
    customerTaxCode: '0305678901',
    contacts: [
      { value: '5', label: 'Hoàng Văn V', phone: '0945678901' },
      { value: '6', label: 'Ngô Thị U', phone: '0956789012' },
    ],
  },
];

// Dữ liệu giả cho danh sách sản phẩm
const mockCatalogs = [
  { id: '1', code: 'SP001', name: 'Sản phẩm A', unit: 'Cái', quantity: 1, unitPrice: 100000, vat: 10 },
  { id: '2', code: 'SP002', name: 'Sản phẩm B', unit: 'Hộp', quantity: 1, unitPrice: 200000, vat: 10 },
  { id: '3', code: 'SP003', name: 'Sản phẩm C', unit: 'Cái', quantity: 1, unitPrice: 150000, vat: 10 },
  { id: '4', code: 'SP004', name: 'Sản phẩm D', unit: 'Bộ', quantity: 1, unitPrice: 300000, vat: 10 },
  { id: '5', code: 'SP005', name: 'Sản phẩm E', unit: 'Cái', quantity: 1, unitPrice: 250000, vat: 10 },
  { id: '6', code: 'SP006', name: 'Sản phẩm F', unit: 'Hộp', quantity: 1, unitPrice: 180000, vat: 10 },
  { id: '7', code: 'SP007', name: 'Sản phẩm G', unit: 'Cái', quantity: 1, unitPrice: 120000, vat: 10 },
  { id: '8', code: 'SP008', name: 'Sản phẩm H', unit: 'Bộ', quantity: 1, unitPrice: 350000, vat: 10 },
];

const CreateQuotationModal = ({ open, onClose, onSuccess }: CreateQuotationModalProps) => {
  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [customerContacts, setCustomerContacts] = useState<{ value: string; label: string; phone: string }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [openAddProductModal, setOpenAddProductModal] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [quotationProducts, setQuotationProducts] = useState<any[]>([]);

  // Tính tổng trị giá
  const calculateTotalValue = useCallback(() => {
    return quotationProducts.reduce((total, product) => {
      const totalAfterVat = product.totalAfterVat || 0;
      return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
    }, 0);
  }, [quotationProducts]);

  // Cập nhật tổng trị giá trong form mỗi khi quotationProducts thay đổi
  useEffect(() => {
    const totalAmount = calculateTotalValue();
    form.setFieldValue('totalAmount', totalAmount);
  }, [quotationProducts, form, calculateTotalValue]);

  useEffect(() => {
    form.setFieldsValue({
      companyAddress: '66 Đường D, Phường Tân Phú, Quận 9, TP.HCM',
      companyTaxCode: '030000000000',
      companyContactPerson: 'Nguyễn Văn A',
      companyPhone: '0909090909',
      companyEmail: '<EMAIL>',
      quotationDate: dayjs(new Date()),
      companyName: 'Công ty TNHH ABC',
    });
  }, [open, form]);

  const handleSubmit = async (values: any) => {
    if (!values) return;

    setIsLoading(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      const body = {
        ...values,
        quotationProducts,
      };

      console.log('Đã tạo báo giá mới:', body);

      setIsLoading(false);
      onClose();
      if (onSuccess) onSuccess();

      // Reset form và dữ liệu
      form.resetFields();
      setFileList([]);
      setQuotationProducts([]);
    }, 1000);
  };

  // Xử lý khi chọn khách hàng
  const handleCustomerChange = (value: string) => {
    const customer = mockCustomers.find(c => c.value === value);

    form.setFieldsValue({
      customerAddress: customer?.customerAddress || '',
      customerTaxCode: customer?.customerTaxCode || '',
      customerContactPerson: undefined,
      customerPhone: '',
    });

    setCustomerContacts(customer?.contacts || []);
  };

  // Xử lý khi chọn contact
  const handleContactChange = (value: string) => {
    const contact = customerContacts.find(c => c.value === value);
    form.setFieldValue('customerPhone', contact?.phone || '');
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[], selectedRows: any[]) => {
    setSelectedRowKeys(newSelectedRowKeys);

    // Tính toán các giá trị cho sản phẩm được chọn
    const calculatedProducts = selectedRows.map(product => {
      const quantity = product.quantity || 1;
      const unitPrice = product.unitPrice || 0;
      const vat = product.vat || 10;
      const totalBeforeVat = quantity * unitPrice;
      const totalAfterVat = Math.round(quantity * unitPrice * (1 + vat / 100));

      return {
        ...product,
        quantity,
        unitPrice,
        vat,
        totalBeforeVat,
        totalAfterVat,
      };
    });

    setSelectedProducts(calculatedProducts);
  };

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    onChange: onSelectChange,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  };

  const handleAddSelectedProducts = () => {
    // Thay thế toàn bộ quotationProducts bằng selectedProducts mới
    setQuotationProducts([...selectedProducts]);
    setSelectedRowKeys([]);
    setSelectedProducts([]);
    setOpenAddProductModal(false);
  };

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      render: (value, record, index) => (
        <InputNumber
          min={1}
          defaultValue={value}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const unitPrice = newProducts[index].unitPrice || 0;
            const vat = newProducts[index].vat;
            const quantity = val || 1;
            newProducts[index] = {
              ...newProducts[index],
              quantity,
              totalBeforeVat: quantity * unitPrice,
              totalAfterVat: quantity * unitPrice * (1 + Number(vat) / 100),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          defaultValue={value || 0}
          style={{ width: '100%' }}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
          parser={value => value!.replace(/\$\s?|(,*)/g, '')}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const quantity = newProducts[index].quantity || 1;
            const vat = newProducts[index].vat || 0;
            const unitPrice = val || 0;
            newProducts[index] = {
              ...newProducts[index],
              unitPrice,
              totalBeforeVat: quantity * unitPrice,
              totalAfterVat: quantity * unitPrice * (1 + Number(vat) / 100),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'totalBeforeVat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'VAT (%)',
      dataIndex: 'vat',
      render: (value, record, index) => (
        <InputNumber
          min={0}
          max={100}
          defaultValue={value || 0}
          onChange={val => {
            const newProducts = [...quotationProducts];
            const quantity = newProducts[index].quantity || 1;
            const unitPrice = newProducts[index].unitPrice || 0;
            const vat = val || 0;
            newProducts[index] = {
              ...newProducts[index],
              vat,
              totalAfterVat: Math.round(quantity * unitPrice * (1 + vat / 100)),
            };
            setQuotationProducts(newProducts);
          }}
        />
      ),
    },
    {
      title: 'Thành tiền sau VAT',
      dataIndex: 'totalAfterVat',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Tác vụ',
      align: 'center' as const,
      fixed: 'right' as const,
      render: (_, __, index) => (
        <Button
          danger
          size="small"
          onClick={() => {
            const newProducts = quotationProducts.filter((_, i) => i !== index);
            setQuotationProducts(newProducts);
          }}
        >
          Xóa
        </Button>
      ),
    },
  ];

  const columnsAddProduct: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
      align: 'center',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      align: 'right',
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      align: 'right',
      render: value => formatMoneyVND(value),
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'unitPrice',
      align: 'right',
      render: (value, record) => formatMoneyVND((record.quantity || 1) * value),
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Card title="Công ty">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Tên công ty" name="companyName">
              <Input placeholder="Nhập tên công ty" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Địa chỉ" name="companyAddress">
              <Input placeholder="Nhập địa chỉ công ty" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Mã số thuế" name="companyTaxCode">
              <Input value={form.getFieldValue('companyTaxCode')} placeholder="Nhập mã số thuế" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Người liên hệ" name="companyContactPerson">
              <Input placeholder="Nhập tên người liên hệ" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Điện thoại" name="companyPhone">
              <Input placeholder="Nhập số điện thoại" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Email" name="companyEmail">
              <Input placeholder="Nhập email" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Số báo giá" name="quotationNumber">
              <Input placeholder="Nhập số báo giá" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Ngày báo giá"
              name="quotationDate"
              required
              rules={[{ required: true, message: 'Vui lòng chọn ngày báo giá' }]}
            >
              <DatePicker placeholder="Ngày báo giá" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card title="Thông tin khách hàng">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Khách hàng" name="customerId" rules={[{ required: true }]}>
              <Select placeholder="Chọn khách hàng" options={mockCustomers} onChange={handleCustomerChange} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Địa chỉ" name="customerAddress">
              <Input placeholder="Địa chỉ khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Mã số thuế" name="customerTaxCode">
              <Input placeholder="Mã số thuế khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={16}>
            <Form.Item
              label="Người liên hệ"
              name="contacts"
              required
              rules={[{ required: true, message: 'Vui lòng chọn người liên hệ' }]}
            >
              <Select
                placeholder="Chọn người liên hệ"
                options={customerContacts}
                onChange={handleContactChange}
                disabled={!customerContacts.length}
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Điện thoại" name="customerPhone">
              <Input placeholder="Số điện thoại liên hệ" disabled />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin hàng hoá"
        extra={
          <Button
            type="primary"
            onClick={() => {
              // Khi mở modal, set lại selectedRowKeys và selectedProducts từ quotationProducts hiện tại
              const currentProductIds = quotationProducts.map(product => product.id);
              const currentSelectedProducts = quotationProducts.map(product => ({
                ...product,
                quantity: product.quantity || 1,
              }));
              setSelectedRowKeys(currentProductIds);
              setSelectedProducts(currentSelectedProducts);
              setOpenAddProductModal(true);
            }}
          >
            Thêm
          </Button>
        }
      >
        <BaseTable
          columns={columns}
          data={quotationProducts}
          total={quotationProducts?.length}
          isLoading={false}
          scroll={{ x: 'max-content' }}
        />

        {/* Tổng trị giá */}
        {quotationProducts && quotationProducts.length > 0 && (
          <div
            style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              fontWeight: 'bold',
            }}
          >
            <span style={{ fontSize: '16px' }}>Tổng trị giá:</span>
            <span style={{ fontSize: '18px', color: '#1890ff' }}>
              {formatMoneyVND(
                quotationProducts.reduce((total, product) => {
                  const totalAfterVat = product.totalAfterVat || 0;
                  return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
                }, 0),
              )}
            </span>
          </div>
        )}
      </Card>
      <Divider />
      <Card title="Điều kiện báo giá">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Thời gian giao hàng" name="deliveryDate">
              <DatePicker placeholder="Chọn ngày giao hàng" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Địa điểm giao hàng"
              name="deliveryLocation"
              rules={[{ required: true, message: 'Vui lòng nhập địa điểm giao hàng' }]}
            >
              <Input placeholder="Nhập địa điểm giao hàng" />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Hiệu lực báo giá (ngày)"
              name="validityDays"
              required
              rules={[{ required: true, message: 'Vui lòng nhập số ngày hiệu lực' }]}
            >
              <InputNumber min={1} placeholder="Nhập số ngày hiệu lực" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Tổng trị giá báo giá">
              <Input
                value={formatMoneyVND(calculateTotalValue())}
                disabled
                style={{
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#1890ff',
                }}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="Ghi chú" name="notes">
              <Input.TextArea rows={3} placeholder="Nhập ghi chú" />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* Trường ẩn để lưu tổng trị giá */}
      <Form.Item name="totalAmount" style={{ display: 'none' }}>
        <Input type="hidden" />
      </Form.Item>
      <div
        style={{
          textAlign: 'right',
          paddingTop: 24,
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isLoading}>
          Lưu
        </Button>
      </div>
    </Form>
  );

  const modalContentAddProduct = (
    <div>
      <Table
        columns={columnsAddProduct}
        dataSource={mockCatalogs}
        rowSelection={rowSelection}
        rowKey="id"
        scroll={{ x: 'max-content' }}
        pagination={{ pageSize: 10 }}
      />
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button onClick={() => setOpenAddProductModal(false)} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" onClick={handleAddSelectedProducts} disabled={selectedProducts.length === 0}>
          Thêm sản phẩm ({selectedProducts.length})
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <BaseModal
        open={open}
        title="Tạo báo giá"
        description="Thêm báo giá mới vào hệ thống"
        onClose={onClose}
        width={2000}
        childrenBody={modalContent}
      />
      <BaseModal
        open={openAddProductModal}
        title="Thêm hàng hoá"
        description="Thêm hàng hoá vào hệ thống"
        onClose={() => setOpenAddProductModal(false)}
        width={1200}
        childrenBody={modalContentAddProduct}
      />
    </>
  );
};

export default CreateQuotationModal;
