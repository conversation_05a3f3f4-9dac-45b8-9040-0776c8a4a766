import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Collapse, Form, Input, Row, Select } from 'antd';
import { FC, useCallback } from 'react';
import { IDepartmentFilter } from '~/api/settting/department/types';

interface IProps {
  onFilter: (values: IDepartmentFilter) => void;
  onReset: () => void;
  isLoading: boolean;
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props;
  const [form] = Form.useForm();

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue());
  }, [form, onFilter]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  return (
    <Collapse>
      <Collapse.Panel header="Tì<PERSON> kiếm" key="0">
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name="id" label="Mã phòng ban">
                <Input placeholder="Nhập mã phòng ban" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="name" label="Tên phòng ban">
                <Input placeholder="Nhập tên phòng ban" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="description" label="Mô tả">
                <Input placeholder="Nhập mô tả" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="status" label="Trạng thái">
                <Select placeholder="Nhập trạng thái" allowClear>
                  <Select.Option value="Hoạt động">Hoạt động</Select.Option>
                  <Select.Option value="Không hoạt động">Không hoạt động</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Row gutter={24} style={{ marginTop: 10 }}>
          <Form.Item style={{ width: '100%' }}>
            <div
              style={{
                display: 'flex',
                gap: 10,
                justifyContent: 'center',
              }}
            >
              <Button
                type="primary"
                style={{ width: '15%' }}
                htmlType="submit"
                onClick={handleFilter}
                loading={isLoading}
              >
                <SearchOutlined />
                Tìm kiếm
              </Button>
              <Button type="default" style={{ width: '15%' }} htmlType="submit" onClick={handleReset}>
                <ReloadOutlined />
                Làm mới
              </Button>
            </div>
          </Form.Item>
        </Row>
      </Collapse.Panel>
    </Collapse>
  );
};

export default FilterProduct;
