import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Collapse, Form, Input, Row } from 'antd';
import { FC, useCallback } from 'react';
import { IEmployeeFilter } from '~/api/settting/employee/types';

interface IProps {
  onFilter: (values: IEmployeeFilter) => void;
  onReset: () => void;
  isLoading: boolean;
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props;
  const [form] = Form.useForm();

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue());
  }, [form, onFilter]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  return (
    <Collapse>
      <Collapse.Panel header="Tìm kiếm" key="0">
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name="employeeCode" label="Mã nhân viên">
                <Input placeholder="Nhập mã nhân viên" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="name" label="Tên nhân viên">
                <Input placeholder="Nhập tên nhân viên" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="email" label="Email">
                <Input placeholder="Nhập email" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="phone" label="Số điện thoại">
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="department" label="Phòng ban">
                <Input placeholder="Nhập phòng ban" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="position" label="Chức vụ">
                <Input placeholder="Nhập chức vụ" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Row gutter={24} style={{ marginTop: 10 }}>
          <Form.Item style={{ width: '100%' }}>
            <div
              style={{
                display: 'flex',
                gap: 10,
                justifyContent: 'center',
              }}
            >
              <Button
                type="primary"
                style={{ width: '15%' }}
                htmlType="submit"
                onClick={handleFilter}
                loading={isLoading}
              >
                <SearchOutlined />
                Tìm kiếm
              </Button>
              <Button type="default" style={{ width: '15%' }} htmlType="submit" onClick={handleReset}>
                <ReloadOutlined />
                Làm mới
              </Button>
            </div>
          </Form.Item>
        </Row>
      </Collapse.Panel>
    </Collapse>
  );
};

export default FilterProduct;
