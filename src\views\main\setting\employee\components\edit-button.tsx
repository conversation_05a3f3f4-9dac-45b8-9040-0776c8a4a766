import { EditOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, DatePicker, Form, Input, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';

import moment from 'moment';
import { useEffect } from 'react';
import { IEmployee } from '~/api/settting/employee/types';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { useModal } from '~/hooks/use-modal';

const { Option } = Select;
const { TextArea } = Input;

interface EditButtonProps {
  data: IEmployee;
  onSuccess?: () => void;
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal();
  const [form] = useForm();
  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data,
        dateOfBirth: data.dateOfBirth ? moment(data.dateOfBirth) : undefined,
      });
    }
  }, [open, data, form]);

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size="small">
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Mã nhân viên" name="employeeCode">
                <Input disabled placeholder="Nhập mã nhân viên" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Họ và tên" name="name">
                <Input placeholder="Nhập họ và tên" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Email" name="email">
                <Input placeholder="Nhập email" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Số điện thoại" name="phone">
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Phòng ban" name="department">
                <Input placeholder="Nhập phòng ban" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Chức vụ" name="position">
                <Input placeholder="Nhập chức vụ" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Ngày sinh" name="dateOfBirth">
                <DatePicker format="DD/MM/YYYY" placeholder="Chọn ngày sinh" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Giới tính" name="gender">
                <Select placeholder="Chọn giới tính">
                  <Option value="male">Nam</Option>
                  <Option value="female">Nữ</Option>
                  <Option value="other">Khác</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Địa chỉ" name="address">
                <Input placeholder="Nhập địa chỉ" />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Ghi chú" name="note">
                <TextArea rows={4} placeholder="Nhập ghi chú" />
              </Form.Item>
            </Col>

            <Col span={24} style={{ textAlign: 'right' }}>
              <Button type="default" style={{ marginRight: 8 }}>
                Lưu
              </Button>
              <Button type="primary">Lưu và tiếp tục cập nhật</Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type="primary" tooltip="Chỉnh sửa" />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa nhân viên"
        description="Cập nhật thông tin nhân viên"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
