import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Form, Input, InputNumber, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { NSCatalog } from '~/common/enums/catalog.enum';
import BaseButton from '~/components/base-button';
import BaseModal from '~/components/base-modal';
import { Catalog } from '..';

const { Option } = Select;
const { TextArea } = Input;

interface EditButtonProps {
  data: Catalog;
  onSuccess?: () => void;
  onClose?: () => void;
}

// Dữ liệu giả cho danh sách sản phẩm (có thể dùng chung với các component khác)
const mockCatalogs = [
  {
    id: '1',
    code: 'SP001',
    name: 'Sản phẩm A',
    type: 'PRODUCT',
    unit: 'PIECE',
    unitPrice: 100000,
    currency: 'VND',
    status: 'ACTIVE',
    createdDate: new Date().toISOString(),
    description: '<PERSON><PERSON> tả sản phẩm A',
  },
  {
    id: '2',
    code: 'SP002',
    name: 'Sản phẩm B',
    type: 'PRODUCT',
    unit: 'BOX',
    unitPrice: 200000,
    currency: 'VND',
    status: 'ACTIVE',
    createdDate: new Date().toISOString(),
    description: 'Mô tả sản phẩm B',
  },
];

const EditButton = ({ data, onClose, onSuccess }: EditButtonProps) => {
  const [open, setOpen] = useState(false);
  const [form] = useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const openModal = () => {
    setOpen(true);
  };

  const closeModal = () => {
    setOpen(false);
    if (onClose) onClose();
  };

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data,
      });
    }
  }, [open, data, form]);

  const handleSave = async (values: any) => {
    setIsSubmitting(true);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      // Tìm và cập nhật sản phẩm trong danh sách giả
      const index = mockCatalogs.findIndex(item => item.id === data.id);
      if (index !== -1) {
        mockCatalogs[index] = { ...mockCatalogs[index], ...values };
        console.log('Đã cập nhật sản phẩm:', mockCatalogs[index]);
        console.log('Danh sách sản phẩm hiện tại:', mockCatalogs);
      }

      setIsSubmitting(false);
      closeModal();
      if (onSuccess) onSuccess();
    }, 500);
  };

  const modalContent = (
    <div>
      <Card style={{ marginBottom: '16px' }} size="small">
        <Form form={form} layout="vertical" onFinish={handleSave}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Tên sản phẩm"
                name="name"
                rules={[{ required: true, message: 'Vui lòng nhập tên sản phẩm' }]}
              >
                <Input placeholder="Nhập tên sản phẩm" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Đơn vị tính"
                name="unit"
                rules={[{ required: true, message: 'Vui lòng chọn đơn vị tính' }]}
              >
                <Select placeholder="Chọn đơn vị tính">
                  {Object.values(NSCatalog.EUnit).map(item => (
                    <Select.Option key={item.code} value={item.code}>
                      {item.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="Đơn giá"
                name="unitPrice"
                rules={[{ required: true, message: 'Vui lòng nhập đơn giá' }]}
              >
                <InputNumber
                  min={0}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  style={{ width: '100%' }}
                  placeholder="Nhập đơn giá"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Mô tả" name="description">
                <TextArea rows={4} placeholder="Nhập mô tả" />
              </Form.Item>
            </Col>
          </Row>
          <div
            style={{
              textAlign: 'right',
              marginTop: 24,
              borderTop: '1px solid #f0f0f0',
              paddingTop: 16,
            }}
          >
            <Button onClick={closeModal} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isSubmitting}>
              Cập nhật
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton icon={<EditOutlined />} onClick={openModal} type="primary" tooltip="Chỉnh sửa" />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Cập nhật sản phẩm"
        description="Cập nhật thông tin sản phẩm"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
