import { EditOutlined, SaveOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Divider, Form, Input, InputNumber, Row, Select } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { ColumnsType } from 'antd/es/table';
import { Table } from 'antd/lib';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import { formatMoneyVND } from '~/common/helpers/helper';
import BaseModal from '~/components/base-modal';
import BaseTable from '~/components/base-table';

interface QuotationEditComponentProps {
  data: any;
}

// Dữ liệu mẫu cho khách hàng
const mockCustomers = [
  {
    value: 'cus001',
    label: 'Công ty TNHH ABC',
    customerAddress: '456 Đường Nguyễn <PERSON>, Quận 1, TP.HCM',
    customerTaxCode: '0987654321',
    contacts: [
      { value: 'contact1', label: '<PERSON>r<PERSON><PERSON> Thị B', phone: '0909876543' },
      { value: 'contact2', label: 'Nguyễn <PERSON>n <PERSON>', phone: '0901234567' },
    ],
  },
  {
    value: 'cus002',
    label: 'Công ty CP XYZ',
    customerAddress: '789 Đường Lê Lợi, Quận 3, TP.HCM',
    customerTaxCode: '0123456789',
    contacts: [
      { value: 'contact3', label: 'Lê Văn D', phone: '0912345678' },
      { value: 'contact4', label: 'Phạm Thị E', phone: '0923456789' },
    ],
  },
];

// Dữ liệu mẫu cho danh mục sản phẩm
const mockCatalogs = [
  {
    id: 'cat001',
    code: 'SP001',
    name: 'Máy tính xách tay Dell XPS 13',
    unit: 'Chiếc',
    unitPrice: 25000000,
    type: 'Sản phẩm',
    description: 'Laptop cao cấp',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
  {
    id: 'cat002',
    code: 'SP002',
    name: 'Màn hình Dell 27 inch',
    unit: 'Chiếc',
    unitPrice: 5000000,
    type: 'Sản phẩm',
    description: 'Màn hình độ phân giải cao',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
  {
    id: 'cat003',
    code: 'SP003',
    name: 'Bàn phím cơ Logitech',
    unit: 'Chiếc',
    unitPrice: 2000000,
    type: 'Sản phẩm',
    description: 'Bàn phím cơ học',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
];

// Dữ liệu mẫu cho chi tiết báo giá
const mockDetailQuotation = {
  id: '1',
  quotationNumber: 'BG-2023-001',
  quotationDate: '2023-10-15',
  companyName: 'Công ty TNHH Apetechs',
  companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
  companyTaxCode: '0123456789',
  companyContactPerson: 'Nguyễn Văn A',
  companyPhone: '0901234567',
  companyEmail: '<EMAIL>',
  customerId: 'cus001',
  customerName: 'Công ty TNHH ABC',
  customerAddress: '456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
  customerTaxCode: '0987654321',
  customerContactPerson: 'Trần Thị B',
  customerPhone: '0909876543',
  contacts: 'contact1',
  deliveryDate: '2023-10-30',
  deliveryLocation: 'Kho hàng Công ty ABC, 456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
  paymentMethod: 'Chuyển khoản ngân hàng',
  validityDays: 30,
  totalAmount: 82500000,
  notes: 'Báo giá có hiệu lực trong vòng 30 ngày kể từ ngày phát hành.',
};

// Dữ liệu mẫu cho các sản phẩm trong báo giá
const mockQuoteItems = [
  {
    id: 'item001',
    catalogId: 'cat001',
    code: 'SP001',
    name: 'Máy tính xách tay Dell XPS 13',
    unit: 'Chiếc',
    quantity: 2,
    unitPrice: 25000000,
    totalBeforeVat: 50000000,
    vat: 10,
    totalAfterVat: 55000000,
    type: 'Sản phẩm',
    description: 'Laptop cao cấp',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
  {
    id: 'item002',
    catalogId: 'cat002',
    code: 'SP002',
    name: 'Màn hình Dell 27 inch',
    unit: 'Chiếc',
    quantity: 3,
    unitPrice: 5000000,
    totalBeforeVat: 15000000,
    vat: 10,
    totalAfterVat: 16500000,
    type: 'Sản phẩm',
    description: 'Màn hình độ phân giải cao',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
  {
    id: 'item003',
    catalogId: 'cat003',
    code: 'SP003',
    name: 'Bàn phím cơ Logitech',
    unit: 'Chiếc',
    quantity: 5,
    unitPrice: 2000000,
    totalBeforeVat: 10000000,
    vat: 10,
    totalAfterVat: 11000000,
    type: 'Sản phẩm',
    description: 'Bàn phím cơ học',
    currency: 'VND',
    images: [],
    attachments: [],
    status: 'active',
  },
];

const QuotationEditComponent = ({ data }: QuotationEditComponentProps) => {
  const [open, setOpen] = useState(false);
  const [form] = useForm();
  const [customerContacts, setCustomerContacts] = useState<{ value: string; label: string; phone: string }[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [qouteItems, setQouteItems] = useState(mockQuoteItems);
  const [isLoadingCatalogs, setIsLoadingCatalogs] = useState(false);
  const [openAddProductModal, setOpenAddProductModal] = useState(false);
  const [quotationProducts, setQuotationProducts] = useState<any>(mockQuoteItems);
  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);
  const [tempEditData, setTempEditData] = useState<any>(null);

  // Mở modal
  const openModal = () => {
    setOpen(true);
    // Giả lập việc lấy dữ liệu
    setTimeout(() => {
      form.setFieldsValue({
        ...mockDetailQuotation,
        quotationDate: dayjs(mockDetailQuotation.quotationDate),
        deliveryDate: dayjs(mockDetailQuotation.deliveryDate),
      });

      // Thiết lập danh sách liên hệ của khách hàng
      const customer = mockCustomers.find(c => c.value === mockDetailQuotation.customerId);
      if (customer) {
        setCustomerContacts(customer.contacts);
      }
    }, 100);
  };

  // Đóng modal
  const closeModal = () => {
    setOpen(false);
  };

  // Tính tổng trị giá
  const calculateTotalValue = useCallback(() => {
    return quotationProducts.reduce((total: number, product: any) => {
      const totalAfterVat = product.totalAfterVat || 0;
      return total + (typeof totalAfterVat === 'string' ? parseFloat(totalAfterVat) : totalAfterVat);
    }, 0);
  }, [quotationProducts]);

  // Cập nhật tổng trị giá trong form mỗi khi quotationProducts thay đổi
  useEffect(() => {
    const totalAmount = calculateTotalValue();
    form.setFieldValue('totalAmount', totalAmount);
  }, [quotationProducts, form, calculateTotalValue]);

  const handleSubmit = async (values: any) => {
    if (!values) return;

    setIsSubmitting(true);

    // Giả lập việc cập nhật báo giá
    setTimeout(() => {
      setIsSubmitting(false);
      closeModal();
      form.resetFields();
      setQuotationProducts([]);
    }, 1000);
  };

  // Xử lý khi chọn khách hàng
  const handleCustomerChange = (value: string) => {
    const customer = mockCustomers.find(c => c.value === value);

    form.setFieldsValue({
      customerAddress: customer?.customerAddress || '',
      customerTaxCode: customer?.customerTaxCode || '',
      customerContactPerson: undefined,
      customerPhone: '',
    });

    setCustomerContacts(customer?.contacts || []);
  };

  // Xử lý khi chọn contact
  const handleContactChange = (value: string) => {
    const contact = customerContacts.find(c => c.value === value);
    form.setFieldValue('customerPhone', contact?.phone || '');
  };

  // Hàm bắt đầu chỉnh sửa hàng
  const handleStartEdit = (record: any, index: number) => {
    setEditingRowIndex(index);
    setTempEditData({ ...record });
  };

  // Hàm lưu thay đổi
  const handleSaveEdit = async (index: number) => {
    if (tempEditData) {
      const newItems = [...qouteItems];
      newItems[index] = tempEditData;
      setQouteItems(newItems);
      setQuotationProducts(newItems);
    }
    setEditingRowIndex(null);
    setTempEditData(null);
  };

  // Hàm hủy chỉnh sửa
  const handleCancelEdit = () => {
    setEditingRowIndex(null);
    setTempEditData(null);
  };

  // Hàm cập nhật dữ liệu tạm thời
  const updateTempData = (field: string, value: any) => {
    if (tempEditData) {
      const updatedData = { ...tempEditData, [field]: value };

      // Tính toán lại các giá trị liên quan
      if (field === 'quantity' || field === 'unitPrice' || field === 'vat') {
        const quantity = field === 'quantity' ? value : updatedData.quantity || 1;
        const unitPrice = field === 'unitPrice' ? value : updatedData.unitPrice || 0;
        const vat = field === 'vat' ? value : updatedData.vat || 0;

        updatedData.totalBeforeVat = quantity * unitPrice;
        updatedData.totalAfterVat = Math.round(quantity * unitPrice * (1 + vat / 100));
      }

      setTempEditData(updatedData);
    }
  };

  // Lọc danh mục sản phẩm chưa được thêm vào báo giá
  const filteredCatalogs = mockCatalogs.filter(
    catalog => !qouteItems.some(product => product.catalogId === catalog.id),
  );

  // Xử lý xóa sản phẩm
  const handleDeleteItem = (id: string) => {
    const newItems = qouteItems.filter(item => item.id !== id);
    setQouteItems(newItems);
    setQuotationProducts(newItems);
  };

  // Xử lý thêm sản phẩm mới
  const handleAddItem = (catalog: any) => {
    const newItem = {
      id: `item${Date.now()}`,
      catalogId: catalog.id,
      code: catalog.code,
      name: catalog.name,
      unit: catalog.unit,
      quantity: 1,
      unitPrice: catalog.unitPrice,
      totalBeforeVat: catalog.unitPrice,
      vat: 10,
      totalAfterVat: Math.round(catalog.unitPrice * 1.1),
      type: catalog.type,
      description: catalog.description,
      currency: catalog.currency,
      images: catalog.images,
      attachments: catalog.attachments,
      status: catalog.status,
    };

    const newItems = [...qouteItems, newItem];
    setQouteItems(newItems);
    setQuotationProducts(newItems);
    setOpenAddProductModal(false);
  };

  const columns: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.quantity : value;

        return isEditing ? (
          <InputNumber
            min={1}
            value={displayValue || 1}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
            onChange={val => updateTempData('quantity', val || 1)}
          />
        ) : (
          <span>{displayValue || 1}</span>
        );
      },
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.unitPrice : value;

        return isEditing ? (
          <InputNumber
            min={0}
            value={displayValue || 0}
            style={{ width: '100%' }}
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, '.')}
            parser={value => value!.replace(/\$\s?|(,*)/g, '')}
            onChange={val => updateTempData('unitPrice', val || 0)}
          />
        ) : (
          <span>{formatMoneyVND(displayValue || 0)}</span>
        );
      },
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'totalBeforeVat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.totalBeforeVat : value;
        return formatMoneyVND(displayValue || 0);
      },
    },
    {
      title: 'VAT (%)',
      dataIndex: 'vat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.vat : value;

        return isEditing ? (
          <InputNumber min={0} max={100} value={displayValue || 0} onChange={val => updateTempData('vat', val || 0)} />
        ) : (
          <span>{displayValue || 0}%</span>
        );
      },
    },
    {
      title: 'Thành tiền sau VAT',
      dataIndex: 'totalAfterVat',
      render: (value, record, index) => {
        const isEditing = editingRowIndex === index;
        const displayValue = isEditing ? tempEditData?.totalAfterVat : value;
        return formatMoneyVND(displayValue || 0);
      },
    },
    {
      title: 'Tác vụ',
      align: 'center' as const,
      fixed: 'right' as const,
      width: 120,
      render: (_, record, index) => {
        const isEditing = editingRowIndex === index;

        return (
          <div style={{ display: 'flex', gap: '8px' }}>
            {isEditing ? (
              <>
                <Button icon={<SaveOutlined />} onClick={() => handleSaveEdit(index)} loading={isSubmitting}>
                  Lưu
                </Button>
                <Button onClick={handleCancelEdit}>Hủy</Button>
              </>
            ) : (
              <>
                <Button type="default" icon={<EditOutlined />} onClick={() => handleStartEdit(record, index)}>
                  Sửa
                </Button>
                <Button danger loading={isSubmitting} onClick={() => handleDeleteItem(record.id)}>
                  Xóa
                </Button>
              </>
            )}
          </div>
        );
      },
    },
  ];

  const columnsAddProduct: ColumnsType<any> = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
    },
    {
      title: 'Sản phẩm',
      dataIndex: 'name',
    },
    {
      title: 'Đơn vị',
      dataIndex: 'unit',
      align: 'center',
    },
    {
      title: 'Đơn giá',
      dataIndex: 'unitPrice',
      render: value => {
        return <span>{formatMoneyVND(value || 0)}</span>;
      },
    },
    {
      title: 'Thành tiền chưa VAT',
      dataIndex: 'unitPrice',
      render: value => {
        return <span>{formatMoneyVND(value || 0)}</span>;
      },
    },
    {
      title: 'Tác vụ',
      dataIndex: 'totalAfterVat',
      render: (_, record) => {
        return (
          <Button type="primary" onClick={() => handleAddItem(record)}>
            Thêm
          </Button>
        );
      },
    },
  ];

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSubmit}>
      <Card title="Công ty">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Tên công ty" name="companyName">
              <Input placeholder="Nhập tên công ty" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Địa chỉ" name="companyAddress">
              <Input placeholder="Nhập địa chỉ công ty" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Mã số thuế" name="companyTaxCode">
              <Input value={form.getFieldValue('companyTaxCode')} placeholder="Nhập mã số thuế" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Người liên hệ" name="companyContactPerson">
              <Input placeholder="Nhập tên người liên hệ" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Điện thoại" name="companyPhone">
              <Input placeholder="Nhập số điện thoại" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Email" name="companyEmail">
              <Input placeholder="Nhập email" disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Số báo giá" name="quotationNumber">
              <Input placeholder="Nhập số báo giá" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Ngày báo giá"
              name="quotationDate"
              required
              rules={[{ required: true, message: 'Vui lòng chọn ngày báo giá' }]}
            >
              <DatePicker placeholder="Ngày báo giá" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card title="Thông tin khách hàng">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Khách hàng" name="customerId" rules={[{ required: true }]}>
              <Select placeholder="Chọn khách hàng" options={mockCustomers} onChange={handleCustomerChange} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Địa chỉ" name="customerAddress">
              <Input placeholder="Địa chỉ khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Mã số thuế" name="customerTaxCode">
              <Input placeholder="Mã số thuế khách hàng" disabled />
            </Form.Item>
          </Col>

          <Col span={16}>
            <Form.Item
              label="Người liên hệ"
              name="contacts"
              required
              rules={[{ required: true, message: 'Vui lòng chọn người liên hệ' }]}
            >
              <Select
                placeholder="Chọn người liên hệ"
                options={customerContacts}
                onChange={handleContactChange}
                disabled={!customerContacts.length}
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Điện thoại" name="customerPhone">
              <Input placeholder="Số điện thoại liên hệ" disabled />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Divider />
      <Card
        title="Thông tin hàng hoá"
        extra={
          <Button
            type="primary"
            onClick={() => {
              setOpenAddProductModal(true);
            }}
          >
            Thêm
          </Button>
        }
      >
        <BaseTable
          columns={columns}
          data={qouteItems}
          total={qouteItems?.length}
          isLoading={isLoadingCatalogs}
          scroll={{ x: 'max-content' }}
        />

        {/* Tổng trị giá */}
        {quotationProducts && quotationProducts.length > 0 && (
          <div
            style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: '#f5f5f5',
              borderRadius: '6px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              fontWeight: 'bold',
            }}
          >
            <span style={{ fontSize: '16px' }}>Tổng trị giá:</span>
            <span style={{ fontSize: '18px', color: '#1890ff' }}>{formatMoneyVND(calculateTotalValue())}</span>
          </div>
        )}
      </Card>
      <Divider />
      <Card title="Điều kiện báo giá">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="Thời gian giao hàng" name="deliveryDate">
              <DatePicker placeholder="Chọn ngày giao hàng" format="YYYY-MM-DD" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Địa điểm giao hàng"
              name="deliveryLocation"
              rules={[{ required: true, message: 'Vui lòng nhập địa điểm giao hàng' }]}
            >
              <Input placeholder="Nhập địa điểm giao hàng" />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="Hiệu lực báo giá (ngày)"
              name="validityDays"
              required
              rules={[{ required: true, message: 'Vui lòng nhập số ngày hiệu lực' }]}
            >
              <InputNumber min={1} placeholder="Nhập số ngày hiệu lực" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Tổng trị giá báo giá">
              <Input
                value={formatMoneyVND(calculateTotalValue())}
                disabled
                style={{
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#1890ff',
                }}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="Ghi chú" name="notes">
              <Input.TextArea rows={3} placeholder="Nhập ghi chú" />
            </Form.Item>
          </Col>
        </Row>
      </Card>
      {/* Trường ẩn để lưu tổng trị giá */}
      <Form.Item name="totalAmount" style={{ display: 'none' }}>
        <Input type="hidden" />
      </Form.Item>
      <div
        style={{
          textAlign: 'right',
          paddingTop: 24,
        }}
      >
        <Button onClick={closeModal} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={isSubmitting}>
          Lưu
        </Button>
      </div>
    </Form>
  );

  const modalContentAddProduct = (
    <div>
      <Table
        columns={columnsAddProduct}
        dataSource={filteredCatalogs}
        scroll={{ x: 'max-content' }}
        pagination={{ pageSize: 10 }}
      />
      <div style={{ textAlign: 'right', marginTop: 16 }}>
        <Button onClick={() => setOpenAddProductModal(false)} style={{ marginRight: 8 }}>
          Hủy
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Button icon={<EditOutlined />} onClick={openModal} type="primary" />
      <BaseModal
        open={open}
        title="Chỉnh sửa báo giá"
        description="Chỉnh sửa báo giá mới vào hệ thống"
        onClose={closeModal}
        width={2000}
        childrenBody={modalContent}
      />
      <BaseModal
        open={openAddProductModal}
        title="Thêm hàng hoá"
        description="Thêm hàng hoá vào hệ thống"
        onClose={() => setOpenAddProductModal(false)}
        width={1200}
        childrenBody={modalContentAddProduct}
      />
    </>
  );
};

export default QuotationEditComponent;
