import {
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
  SendOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Button, Col, Row, Select, Space, Tag } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useNavigate } from 'react-router';
import { QuoteStatus, QuoteStatusOptions } from '~/common/enums/quote.enum';
import { formatDate, formatMoneyVND } from '~/common/helpers/helper';
import BaseCard from '~/components/base-card';
import BaseConfirmButton from '~/components/base-confirm-button';
import BaseFilter from '~/components/base-filter/BaseFilter';
import BaseTable from '~/components/base-table';
import CreateQuotationModal from './components/quotation-create-component';
import QuotationEditComponent from './components/quotation-edit-component';

const { Option } = Select;

// Dữ liệu mẫu cho danh sách báo giá
const mockQuotes = [
  {
    id: '1',
    quotationNumber: 'BG-2023-001',
    customerName: 'Công ty TNHH ABC',
    totalAmount: 82500000,
    quotationDate: '2023-10-15',
    validityDays: 30,
    createdBy: 'Nguyễn Văn A',
    statusCode: QuoteStatus.NEW,
    statusName: 'Mới',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
    customerTaxCode: '0987654321',
    customerContactPerson: 'Trần Thị B',
    customerPhone: '0909876543',
    deliveryDate: '2023-10-30',
    deliveryLocation: 'Kho hàng Công ty ABC, 456 Đường Nguyễn Huệ, Quận 1, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 30 ngày kể từ ngày phát hành.',
  },
  {
    id: '2',
    quotationNumber: 'BG-2023-002',
    customerName: 'Công ty CP XYZ',
    totalAmount: 45000000,
    quotationDate: '2023-10-20',
    validityDays: 15,
    createdBy: 'Trần Thị B',
    statusCode: QuoteStatus.APPROVED,
    statusName: 'Đã duyệt',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '789 Đường Lê Lợi, Quận 3, TP.HCM',
    customerTaxCode: '0123456789',
    customerContactPerson: 'Lê Văn D',
    customerPhone: '0912345678',
    deliveryDate: '2023-11-05',
    deliveryLocation: 'Kho hàng Công ty XYZ, 789 Đường Lê Lợi, Quận 3, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 15 ngày kể từ ngày phát hành.',
  },
  {
    id: '3',
    quotationNumber: 'BG-2023-003',
    customerName: 'Công ty TNHH DEF',
    totalAmount: *********,
    quotationDate: '2023-10-25',
    validityDays: 20,
    createdBy: 'Lê Văn C',
    statusCode: QuoteStatus.SENT,
    statusName: 'Đã gửi',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '101 Đường Nguyễn Du, Quận 1, TP.HCM',
    customerTaxCode: '0345678912',
    customerContactPerson: 'Hoàng Văn E',
    customerPhone: '0934567890',
    deliveryDate: '2023-11-15',
    deliveryLocation: 'Kho hàng Công ty DEF, 101 Đường Nguyễn Du, Quận 1, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 20 ngày kể từ ngày phát hành.',
  },
  {
    id: '4',
    quotationNumber: 'BG-2023-004',
    customerName: 'Công ty CP GHI',
    totalAmount: 75000000,
    quotationDate: '2023-10-30',
    validityDays: 25,
    createdBy: 'Phạm Thị D',
    statusCode: QuoteStatus.CONFIRMED,
    statusName: 'Đã chốt',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '202 Đường Trần Hưng Đạo, Quận 5, TP.HCM',
    customerTaxCode: '0567891234',
    customerContactPerson: 'Ngô Thị F',
    customerPhone: '0945678901',
    deliveryDate: '2023-11-20',
    deliveryLocation: 'Kho hàng Công ty GHI, 202 Đường Trần Hưng Đạo, Quận 5, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 25 ngày kể từ ngày phát hành.',
  },
  {
    id: '5',
    quotationNumber: 'BG-2023-005',
    customerName: 'Công ty TNHH JKL',
    totalAmount: 95000000,
    quotationDate: '2023-11-05',
    validityDays: 30,
    createdBy: 'Hoàng Văn E',
    statusCode: QuoteStatus.REJECTED,
    statusName: 'Từ chối',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '303 Đường Nguyễn Trãi, Quận 1, TP.HCM',
    customerTaxCode: '0678912345',
    customerContactPerson: 'Trịnh Văn G',
    customerPhone: '0956789012',
    deliveryDate: '2023-12-05',
    deliveryLocation: 'Kho hàng Công ty JKL, 303 Đường Nguyễn Trãi, Quận 1, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 30 ngày kể từ ngày phát hành.',
  },
  {
    id: '6',
    quotationNumber: 'BG-2023-006',
    customerName: 'Công ty CP MNO',
    totalAmount: 60000000,
    quotationDate: '2023-11-10',
    validityDays: 15,
    createdBy: 'Ngô Thị F',
    statusCode: QuoteStatus.CANCELLED,
    statusName: 'Đã hủy',
    companyName: 'Công ty TNHH Apetechs',
    companyAddress: '123 Đường Lê Lợi, Quận 1, TP.HCM',
    companyTaxCode: '0123456789',
    companyContactPerson: 'Nguyễn Văn A',
    companyPhone: '0901234567',
    companyEmail: '<EMAIL>',
    customerAddress: '404 Đường Lý Thường Kiệt, Quận 10, TP.HCM',
    customerTaxCode: '0789123456',
    customerContactPerson: 'Lý Thị H',
    customerPhone: '0967890123',
    deliveryDate: '2023-11-25',
    deliveryLocation: 'Kho hàng Công ty MNO, 404 Đường Lý Thường Kiệt, Quận 10, TP.HCM',
    paymentMethod: 'Chuyển khoản ngân hàng',
    notes: 'Báo giá có hiệu lực trong vòng 15 ngày kể từ ngày phát hành.',
  },
];

// Dữ liệu mẫu cho các trường lọc
const mockFilterFields = [
  {
    key: 'quotationNumber',
    name: 'quotationNumber',
    label: 'Số báo giá',
    type: 'text',
    placeholder: 'Nhập số báo giá',
  },
  {
    key: 'customerName',
    name: 'customerName',
    label: 'Khách hàng',
    type: 'text',
    placeholder: 'Nhập tên khách hàng',
  },
  {
    key: 'statusCode',
    name: 'statusCode',
    label: 'Trạng thái',
    type: 'select',
    placeholder: 'Chọn trạng thái',
    selectOptions: Object.keys(QuoteStatusOptions).map(key => ({
      value: key,
      name: QuoteStatusOptions[key as QuoteStatus]?.label,
      label: QuoteStatusOptions[key as QuoteStatus]?.label,
    })),
  },
  {
    key: 'quotationDate',
    name: 'quotationDate',
    label: 'Ngày báo giá',
    type: 'date',
    placeholder: 'Chọn ngày báo giá',
  },
  {
    key: 'createdBy',
    name: 'createdBy',
    label: 'Người tạo',
    type: 'text',
    placeholder: 'Nhập tên người tạo',
  },
];

export const QuotationsComponent = () => {
  const navigate = useNavigate();
  const [filterData, setFilterData] = useState({});
  const [quotes, setQuotes] = useState(mockQuotes);
  const [isLoading, setIsLoading] = useState(false);
  const [isPendingUpdateStatus, setIsPendingUpdateStatus] = useState(false);
  const [visibleCreateModal, setVisibleCreateModal] = useState<boolean>(false);

  // Xử lý lọc dữ liệu
  const handleFilter = (values: any) => {
    setIsLoading(true);

    // Giả lập việc lọc dữ liệu
    setTimeout(() => {
      let filteredData = [...mockQuotes];

      if (values.quotationNumber) {
        filteredData = filteredData.filter(item =>
          item.quotationNumber.toLowerCase().includes(values.quotationNumber.toLowerCase()),
        );
      }

      if (values.customerName) {
        filteredData = filteredData.filter(item =>
          item.customerName.toLowerCase().includes(values.customerName.toLowerCase()),
        );
      }

      if (values.statusCode) {
        filteredData = filteredData.filter(item => item.statusCode === values.statusCode);
      }

      if (values.quotationDate) {
        const filterDate = dayjs(values.quotationDate).format('YYYY-MM-DD');
        filteredData = filteredData.filter(item => dayjs(item.quotationDate).format('YYYY-MM-DD') === filterDate);
      }

      if (values.createdBy) {
        filteredData = filteredData.filter(item =>
          item.createdBy.toLowerCase().includes(values.createdBy.toLowerCase()),
        );
      }

      setQuotes(filteredData);
      setFilterData(values);
      setIsLoading(false);
    }, 500);
  };

  // Xử lý reset bộ lọc
  const handleFilterReset = () => {
    setIsLoading(true);

    // Giả lập việc reset dữ liệu
    setTimeout(() => {
      setQuotes(mockQuotes);
      setFilterData({});
      setIsLoading(false);
    }, 300);
  };

  const handleDetailQuotation = (id: string) => {
    navigate(`detail?id=${id}`);
  };

  // Xử lý cập nhật trạng thái báo giá
  const updateStatus = ({ id, status }: { id: string; status: QuoteStatus }) => {
    setIsPendingUpdateStatus(true);

    // Giả lập việc cập nhật trạng thái
    setTimeout(() => {
      const updatedQuotes = quotes.map(quote => {
        if (quote.id === id) {
          return {
            ...quote,
            statusCode: status,
            statusName: QuoteStatusOptions[status]?.label,
          };
        }
        return quote;
      });

      setQuotes(updatedQuotes);
      setIsPendingUpdateStatus(false);
    }, 500);
  };

  const handleSendQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.SENT });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleDeleteQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.CANCELLED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleApproveQuotation = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.APPROVED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleCustomerAccept = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.CONFIRMED });
    // Có thể thêm thông báo thành công ở đây
  };

  const handleCustomerReject = (id: string) => {
    if (isPendingUpdateStatus) return;
    updateStatus({ id, status: QuoteStatus.REJECTED });
    // Có thể thêm thông báo thành công ở đây
  };

  const renderActions = (record: any) => {
    switch (record.statusCode) {
      case QuoteStatus.NEW:
        return (
          <Space wrap>
            <QuotationEditComponent data={record} />
            <BaseConfirmButton
              danger
              type="default"
              size="middle"
              icon={<DeleteOutlined />}
              onConfirm={() => handleDeleteQuotation(record.id)}
              tooltip="Hủy báo giá"
              confirmTitle="Bạn có chắc chắn muốn hủy báo giá này không?"
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<CheckOutlined />}
              onConfirm={() => handleApproveQuotation(record.id)}
              tooltip="Duyệt báo giá"
              confirmTitle="Bạn có chắc chắn muốn duyệt báo giá này không?"
            />
          </Space>
        );

      case QuoteStatus.CANCELLED:
        return (
          <BaseConfirmButton
            type="primary"
            icon={<EyeOutlined />}
            onConfirm={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
            confirmTitle="Bạn có chắc chắn muốn xem chi tiết báo giá này không?"
          />
        );

      case QuoteStatus.APPROVED:
        return (
          <Space wrap>
            <Button
              type="primary"
              size="middle"
              icon={<EyeOutlined />}
              onClick={() => handleDetailQuotation(record.id)}
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<SendOutlined />}
              onConfirm={() => handleSendQuotation(record.id)}
              tooltip="Gửi khách hàng"
              confirmTitle="Bạn có chắc chắn muốn gửi khách hàng này không?"
            />
          </Space>
        );

      case QuoteStatus.SENT:
        return (
          <Space wrap>
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<CloseOutlined />}
              onConfirm={() => handleCustomerReject(record.id)}
              tooltip="KH Từ chối"
              confirmTitle="Bạn có chắc chắn muốn từ chối báo giá này không?"
            />
            <BaseConfirmButton
              type="primary"
              size="middle"
              icon={<UserOutlined />}
              onConfirm={() => handleCustomerAccept(record.id)}
              tooltip="KH Chốt"
              confirmTitle="Bạn có chắc chắn muốn chốt báo giá này không?"
            />
          </Space>
        );

      case QuoteStatus.CONFIRMED:
        return (
          <BaseConfirmButton
            type="primary"
            size="middle"
            icon={<EyeOutlined />}
            onConfirm={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
            confirmTitle="Bạn có chắc chắn muốn xem chi tiết báo giá này không?"
          />
        );
      case QuoteStatus.REJECTED:
        return (
          <BaseConfirmButton
            type="primary"
            size="middle"
            icon={<EyeOutlined />}
            onConfirm={() => handleDetailQuotation(record.id)}
            tooltip="Xem chi tiết"
            confirmTitle="Bạn có chắc chắn muốn xem chi tiết báo giá này không?"
          />
        );

      default:
        return null;
    }
  };

  const columns = [
    {
      title: 'STT',
      dataIndex: 'stt',
      align: 'center' as const,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: 'Số báo giá',
      dataIndex: 'quotationNumber',
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customerName',
    },
    {
      title: 'Giá trị',
      dataIndex: 'totalAmount',
      render: (value: number) => {
        const totalValue = Math.round(value);
        return formatMoneyVND(totalValue);
      },
    },
    {
      title: 'Ngày báo giá',
      dataIndex: 'quotationDate',
      render: (date: string) => formatDate(date),
    },
    {
      title: 'Hạn báo giá',
      dataIndex: 'quotationDate',
      render: (date: string, record: any) => {
        if (!record.quotationDate || !record.validityDays) {
          return '-';
        }

        const expiryDate = dayjs(record.quotationDate).add(record.validityDays, 'days').format('DD/MM/YYYY - HH:mm');

        return expiryDate;
      },
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
    },
    {
      title: 'Trạng thái',
      dataIndex: 'statusCode',
      render: (status: QuoteStatus) => (
        <Tag
          color={QuoteStatusOptions[status]?.colorTag}
          style={{
            fontSize: '14px',
            padding: '4px 12px',
            width: '100%',
            textAlign: 'center',
          }}
        >
          {QuoteStatusOptions[status]?.label}
        </Tag>
      ),
    },
  ];

  const actionColumn: ColumnsType<any> = [
    {
      title: 'Tác vụ',
      fixed: 'right' as const,
      render: (record: any) => renderActions(record),
    },
  ];

  return (
    <BaseCard
      title="Quản lý báo giá"
      buttons={[
        {
          text: 'Thêm mới báo giá',
          icon: <PlusOutlined />,
          onClick: () => {
            setVisibleCreateModal(true);
          },
          type: 'primary',
        },
      ]}
    >
      {visibleCreateModal && (
        <CreateQuotationModal
          open={visibleCreateModal}
          onClose={() => setVisibleCreateModal(false)}
          onSuccess={() => setVisibleCreateModal(false)}
        />
      )}
      <BaseFilter
        onReset={handleFilterReset}
        isLoading={isLoading}
        filters={mockFilterFields}
        onFilter={handleFilter}
      />
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={[...columns, ...actionColumn]}
            data={quotes}
            total={quotes.length}
            isLoading={isLoading}
            scroll={{ x: 'max-content' }}
          />
        </Col>
      </Row>
    </BaseCard>
  );
};
