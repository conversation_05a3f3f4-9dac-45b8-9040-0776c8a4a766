import { useCallback, useEffect, useState } from 'react';
import { ICatalog, ICreateCatalog, IUpdateCatalog } from '~/api/catalog/types';
import { catalogApi } from '~/api/catalog';
import { IPageRequest } from '~/api/@common';

export const useCatalog = () => {
  const [data, setData] = useState<ICatalog[]>([]);
  const [detailData, setDetailData] = useState<ICatalog | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState<IPageRequest>({
    pageIndex: 1,
    pageSize: 10,
  });

  const loadData = useCallback(() => {
    setIsLoading(true);
    catalogApi
      .list(page)
      .then(result => {
        setData(result.data);
        setTotal(result.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page]);

  // detail
  const detail = useCallback((id: string) => {
    setIsLoading(true);
    catalogApi
      .detail({ id })
      .then(result => {
        setDetailData(result);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  const create = useCallback((data: ICreateCatalog) => {
    setIsLoading(true);
    catalogApi
      .create(data)
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Update
  const update = useCallback((data: IUpdateCatalog) => {
    setIsLoading(true);
    catalogApi
      .update(data)
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Active
  const active = useCallback((id: string) => {
    setIsLoading(true);
    catalogApi
      .active({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  // Inactive
  const inactive = useCallback((id: string) => {
    setIsLoading(true);
    catalogApi
      .inactive({ id })
      .then(result => {
        loadData();
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    data,
    detailData,
    setData,
    detail,
    isLoading,
    setIsLoading,
    total,
    setTotal,
    page,
    setPage,
    create,
    update,
    active,
    inactive,
  };
};
