export type ICatalog = ICatalogItem[];

export interface ICatalogItem {
  key: React.Key;
  id: string;
  tenantId: any;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  code: string;
  name: string;
  type: string;
  description: any;
  unit: string;
  currency: string;
  unitPrice: number;
  taxRate: string;
  images: any;
  attachments: any;
  status: string;
  vat: string;
}
